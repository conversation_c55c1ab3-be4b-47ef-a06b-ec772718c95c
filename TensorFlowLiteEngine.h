#pragma once

#include <QObject>
#include <QImage>
#include <QString>
#include <QStringList>
#include <QRectF>
#include <QTimer>
#include <QColor>
#include <vector>
#include <memory>

/**
 * @brief TensorFlow Lite检测结果
 */
struct TFLiteDetection {
    QString className;      // 类别名称
    float confidence;       // 置信度
    QRectF boundingBox;     // 边界框 (归一化坐标)
    int classId;           // 类别ID
    QColor color;          // 显示颜色

    TFLiteDetection() : confidence(0.0f), classId(-1) {}
    TFLiteDetection(const QString& name, float conf, const QRectF& box, int id, const QColor& c = Qt::green)
        : className(name), confidence(conf), boundingBox(box), classId(id), color(c) {}
};

/**
 * @brief TensorFlow Lite推理结果
 */
struct TFLiteInferenceResult {
    std::vector<TFLiteDetection> detections;  // 检测结果
    std::vector<float> classifications;       // 分类结果
    QStringList classNames;                   // 类别名称列表
    float inferenceTime;                      // 推理耗时(ms)
    bool success;                            // 是否成功
    QString errorMessage;                    // 错误信息

    TFLiteInferenceResult() : inferenceTime(0.0f), success(false) {}
};

/**
 * @brief TensorFlow Lite引擎
 * 
 * 支持图像分类和目标检测模型的加载与推理
 */
class TensorFlowLiteEngine : public QObject {
    Q_OBJECT

public:
    enum ModelType {
        ImageClassification,    // 图像分类
        ObjectDetection,       // 目标检测
        Unknown               // 未知类型
    };

    enum AccelerationType {
        CPU,                  // CPU推理
        GPU,                  // GPU推理（如果支持）
        NNAPI,               // Android NNAPI（如果支持）
        Auto                 // 自动选择
    };

    explicit TensorFlowLiteEngine(QObject* parent = nullptr);
    ~TensorFlowLiteEngine();

    /**
     * @brief 加载TensorFlow Lite模型
     */
    bool loadModel(const QString& modelPath, ModelType type = Unknown);
    
    /**
     * @brief 加载类别标签文件
     */
    bool loadLabels(const QString& labelsPath);

    /**
     * @brief 设置推理加速类型
     */
    void setAccelerationType(AccelerationType type);
    AccelerationType getAccelerationType() const { return accelerationType_; }

    /**
     * @brief 设置推理参数
     */
    void setConfidenceThreshold(float threshold) { confidenceThreshold_ = threshold; }
    void setNMSThreshold(float threshold) { nmsThreshold_ = threshold; }
    void setMaxDetections(int maxDetections) { maxDetections_ = maxDetections; }

    /**
     * @brief 获取模型信息
     */
    ModelType getModelType() const { return modelType_; }
    QString getCurrentModelPath() const { return currentModelPath_; }
    QStringList getClassNames() const { return classNames_; }
    bool isModelLoaded() const { return modelLoaded_; }

    /**
     * @brief 图像分类推理
     */
    TFLiteInferenceResult classifyImage(const QImage& image);

    /**
     * @brief 目标检测推理
     */
    TFLiteInferenceResult detectObjects(const QImage& image);

    /**
     * @brief 通用推理接口
     */
    TFLiteInferenceResult runInference(const QImage& image);

    /**
     * @brief 获取推理统计信息
     */
    struct InferenceStats {
        int totalInferences = 0;
        float averageInferenceTime = 0.0f;
        float minInferenceTime = 0.0f;
        float maxInferenceTime = 0.0f;
        int successfulInferences = 0;
        int failedInferences = 0;
    };

    InferenceStats getInferenceStats() const { return stats_; }
    void resetStats();

    /**
     * @brief 获取可用的预训练模型列表
     */
    static QStringList getAvailableModels();

signals:
    /**
     * @brief 推理完成信号
     */
    void inferenceCompleted(const TFLiteInferenceResult& result);

    /**
     * @brief 推理错误信号
     */
    void inferenceError(const QString& errorMessage);

    /**
     * @brief 推理进度信号
     */
    void inferenceProgress(const QString& status);

    /**
     * @brief 统计信息更新信号
     */
    void statsUpdated(const InferenceStats& stats);

private:
    // 模型相关
    void* interpreter_;                    // tflite::Interpreter*
    void* model_;                         // tflite::FlatBufferModel*
    ModelType modelType_;
    QString currentModelPath_;
    QStringList classNames_;
    bool modelLoaded_;

    // 推理参数
    AccelerationType accelerationType_;
    float confidenceThreshold_;
    float nmsThreshold_;
    int maxDetections_;

    // 输入输出信息
    int inputTensorIndex_;
    int outputTensorIndex_;
    std::vector<int> inputShape_;
    std::vector<int> outputShape_;

    // 统计信息
    mutable InferenceStats stats_;

    // 私有方法
    bool initializeInterpreter();
    bool setupAcceleration();
    QImage preprocessImage(const QImage& image);
    std::vector<float> imageToFloatArray(const QImage& image);
    TFLiteInferenceResult processClassificationOutput();
    TFLiteInferenceResult processDetectionOutput();
    void updateStats(float inferenceTime, bool success);
    
    // 工具方法
    ModelType detectModelType(const QString& modelPath);
    bool validateModel();
    void logModelInfo();
};
