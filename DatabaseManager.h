#pragma once

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QString>
#include <QStringList>
#include <QDateTime>
#include <QVariantMap>
#include <QMutex>
#include <QJsonObject>
#include <QJsonDocument>

/**
 * @brief 识别记录数据结构
 */
struct DetectionRecord {
    int id = -1;                        // 记录ID
    QString sessionId;                  // 会话ID
    QDateTime timestamp;                // 时间戳
    QString imageHash;                  // 图像哈希值
    QString imagePath;                  // 图像路径
    QString modelName;                  // 使用的模型名称
    QString modelType;                  // 模型类型 (classification/detection)
    QString engineType;                 // 引擎类型 (tensorflow_lite/opencv)
    QJsonObject detectionResults;       // 检测结果 (JSON格式)
    float processingTime;               // 处理耗时(ms)
    float confidence;                   // 平均置信度
    int detectionCount;                 // 检测到的对象数量
    QString notes;                      // 备注
    
    DetectionRecord() : processingTime(0.0f), confidence(0.0f), detectionCount(0) {}
};

/**
 * @brief 模型配置数据结构
 */
struct ModelConfig {
    int id = -1;                        // 配置ID
    QString modelName;                  // 模型名称
    QString modelPath;                  // 模型路径
    QString labelsPath;                 // 标签文件路径
    QString modelType;                  // 模型类型
    QString engineType;                 // 引擎类型
    QJsonObject parameters;             // 模型参数
    bool isDefault;                     // 是否为默认模型
    bool isEnabled;                     // 是否启用
    QDateTime createdAt;                // 创建时间
    QDateTime updatedAt;                // 更新时间
    
    ModelConfig() : isDefault(false), isEnabled(true) {}
};

/**
 * @brief 用户设置数据结构
 */
struct UserSettings {
    int id = -1;                        // 设置ID
    QString category;                   // 设置类别
    QString key;                        // 设置键
    QString value;                      // 设置值
    QString description;                // 描述
    QDateTime updatedAt;                // 更新时间
    
    UserSettings() = default;
    UserSettings(const QString& cat, const QString& k, const QString& v, const QString& desc = "")
        : category(cat), key(k), value(v), description(desc), updatedAt(QDateTime::currentDateTime()) {}
};

/**
 * @brief 数据库管理器
 * 
 * 负责管理SQLite数据库，存储识别记录、模型配置、用户设置等数据
 */
class DatabaseManager : public QObject {
    Q_OBJECT

public:
    explicit DatabaseManager(QObject* parent = nullptr);
    ~DatabaseManager();

    /**
     * @brief 初始化数据库
     */
    bool initialize(const QString& databasePath = QString());

    /**
     * @brief 检查数据库连接状态
     */
    bool isConnected() const;

    /**
     * @brief 关闭数据库连接
     */
    void close();

    // ========== 识别记录管理 ==========
    
    /**
     * @brief 添加识别记录
     */
    int addDetectionRecord(const DetectionRecord& record);

    /**
     * @brief 获取识别记录
     */
    DetectionRecord getDetectionRecord(int id) const;

    /**
     * @brief 获取识别记录列表
     */
    QList<DetectionRecord> getDetectionRecords(int limit = 100, int offset = 0) const;

    /**
     * @brief 根据条件查询识别记录
     */
    QList<DetectionRecord> queryDetectionRecords(const QVariantMap& conditions, 
                                                 int limit = 100, int offset = 0) const;

    /**
     * @brief 根据会话ID获取记录
     */
    QList<DetectionRecord> getDetectionRecordsBySession(const QString& sessionId) const;

    /**
     * @brief 根据时间范围获取记录
     */
    QList<DetectionRecord> getDetectionRecordsByTimeRange(const QDateTime& startTime, 
                                                          const QDateTime& endTime) const;

    /**
     * @brief 更新识别记录
     */
    bool updateDetectionRecord(const DetectionRecord& record);

    /**
     * @brief 删除识别记录
     */
    bool deleteDetectionRecord(int id);

    /**
     * @brief 清空识别记录
     */
    bool clearDetectionRecords();

    /**
     * @brief 获取识别记录统计信息
     */
    QVariantMap getDetectionStatistics() const;

    // ========== 模型配置管理 ==========

    /**
     * @brief 添加模型配置
     */
    int addModelConfig(const ModelConfig& config);

    /**
     * @brief 获取模型配置
     */
    ModelConfig getModelConfig(int id) const;
    ModelConfig getModelConfigByName(const QString& modelName) const;

    /**
     * @brief 获取所有模型配置
     */
    QList<ModelConfig> getAllModelConfigs() const;

    /**
     * @brief 获取指定类型的模型配置
     */
    QList<ModelConfig> getModelConfigsByType(const QString& modelType) const;

    /**
     * @brief 获取默认模型配置
     */
    ModelConfig getDefaultModelConfig(const QString& modelType) const;

    /**
     * @brief 更新模型配置
     */
    bool updateModelConfig(const ModelConfig& config);

    /**
     * @brief 删除模型配置
     */
    bool deleteModelConfig(int id);

    /**
     * @brief 设置默认模型
     */
    bool setDefaultModel(const QString& modelName, const QString& modelType);

    // ========== 用户设置管理 ==========

    /**
     * @brief 设置用户配置
     */
    bool setSetting(const QString& category, const QString& key, const QString& value, 
                   const QString& description = "");

    /**
     * @brief 获取用户配置
     */
    QString getSetting(const QString& category, const QString& key, 
                      const QString& defaultValue = "") const;

    /**
     * @brief 获取分类下的所有设置
     */
    QVariantMap getSettings(const QString& category) const;

    /**
     * @brief 获取所有用户设置
     */
    QList<UserSettings> getAllSettings() const;

    /**
     * @brief 删除用户设置
     */
    bool deleteSetting(const QString& category, const QString& key);

    /**
     * @brief 清空指定分类的设置
     */
    bool clearSettings(const QString& category);

    // ========== 数据库维护 ==========

    /**
     * @brief 备份数据库
     */
    bool backupDatabase(const QString& backupPath) const;

    /**
     * @brief 恢复数据库
     */
    bool restoreDatabase(const QString& backupPath);

    /**
     * @brief 优化数据库
     */
    bool optimizeDatabase();

    /**
     * @brief 获取数据库信息
     */
    QVariantMap getDatabaseInfo() const;

    /**
     * @brief 检查数据库完整性
     */
    bool checkDatabaseIntegrity() const;

signals:
    /**
     * @brief 数据库连接状态变化信号
     */
    void connectionStateChanged(bool connected);

    /**
     * @brief 数据库错误信号
     */
    void databaseError(const QString& errorMessage);

    /**
     * @brief 记录添加信号
     */
    void recordAdded(const QString& tableName, int recordId);

    /**
     * @brief 记录更新信号
     */
    void recordUpdated(const QString& tableName, int recordId);

    /**
     * @brief 记录删除信号
     */
    void recordDeleted(const QString& tableName, int recordId);

private:
    QSqlDatabase database_;
    QString databasePath_;
    mutable QMutex mutex_;
    bool connected_;

    // 私有方法
    bool createTables();
    bool createDetectionRecordsTable();
    bool createModelConfigsTable();
    bool createUserSettingsTable();
    bool createIndexes();
    
    bool executeQuery(const QString& queryString, const QVariantList& bindValues = QVariantList()) const;
    QSqlQuery prepareQuery(const QString& queryString) const;
    
    DetectionRecord recordFromQuery(const QSqlQuery& query) const;
    ModelConfig modelConfigFromQuery(const QSqlQuery& query) const;
    UserSettings userSettingsFromQuery(const QSqlQuery& query) const;
    
    void logError(const QString& operation, const QSqlError& error) const;
    QString generateConnectionName() const;
};
