#include "InferenceManager.h"
#include <QDebug>
#include <QThread>
#include <QMutexLocker>
#include <QCoreApplication>
#include <QProcess>
#include <algorithm>

#if defined(_MSC_VER)
#pragma execution_character_set("utf-8")
#endif

InferenceManager::InferenceManager(QObject* parent)
    : QObject(parent)
    , initialized_(false)
    , running_(false)
    , paused_(false)
    , tfliteEngine_(nullptr)
    , modelManager_(nullptr)
    , opencvEngine_(nullptr)
    , statsUpdateTimer_(new QTimer(this))
    , performanceCheckTimer_(new QTimer(this))
    , performanceMonitoringEnabled_(true)
{
    // 连接定时器
    connect(statsUpdateTimer_, &QTimer::timeout, this, &InferenceManager::onStatsUpdateTimer);
    connect(performanceCheckTimer_, &QTimer::timeout, this, &InferenceManager::onPerformanceCheckTimer);
    
    qDebug() << "推理管理器已创建";
}

InferenceManager::~InferenceManager()
{
    stop();
    cleanupWorkers();
    qDebug() << "推理管理器已销毁";
}

bool InferenceManager::initialize(const InferenceManagerConfig& config)
{
    if (initialized_) {
        qDebug() << "推理管理器已初始化";
        return true;
    }
    
    config_ = config;
    
    // 初始化工作线程
    initializeWorkers();
    
    // 设置定时器
    statsUpdateTimer_->setInterval(config_.statsUpdateInterval);
    performanceCheckTimer_->setInterval(5000); // 每5秒检查一次性能
    
    initialized_ = true;
    qDebug() << "推理管理器初始化完成，工作线程数:" << workers_.size();
    
    return true;
}

void InferenceManager::setTensorFlowLiteEngine(TensorFlowLiteEngine* engine)
{
    tfliteEngine_ = engine;
    
    // 更新所有工作线程的引擎
    QMutexLocker locker(&workersMutex_);
    for (auto& worker : workers_) {
        updateWorkerEngines(worker);
    }
    
    qDebug() << "TensorFlow Lite引擎已设置到推理管理器";
}

void InferenceManager::setTensorFlowLiteModelManager(TensorFlowLiteModelManager* manager)
{
    modelManager_ = manager;
    qDebug() << "TensorFlow Lite模型管理器已设置到推理管理器";
}

void InferenceManager::setOpenCVEngine(OpenCVVisionEngine* engine)
{
    opencvEngine_ = engine;
    
    // 更新所有工作线程的引擎
    QMutexLocker locker(&workersMutex_);
    for (auto& worker : workers_) {
        updateWorkerEngines(worker);
    }
    
    qDebug() << "OpenCV引擎已设置到推理管理器";
}

QString InferenceManager::submitTask(InferenceTaskType type, const QImage& image, 
                                   int priority, const QVariantMap& parameters)
{
    if (!running_) {
        qDebug() << "推理管理器未运行，无法提交任务";
        return QString();
    }
    
    // 选择最佳工作线程
    int workerIndex = selectBestWorker();
    if (workerIndex < 0 || workerIndex >= workers_.size()) {
        qDebug() << "没有可用的工作线程";
        return QString();
    }
    
    // 提交任务到选定的工作线程
    QString taskId = workers_[workerIndex]->addTask(type, image, priority, parameters);
    
    if (!taskId.isEmpty()) {
        QMutexLocker locker(&statsMutex_);
        taskToWorkerMap_[taskId] = workerIndex;
        stats_.totalTasks++;
    }
    
    return taskId;
}

QStringList InferenceManager::submitBatchTasks(const QList<InferenceTask>& tasks)
{
    QStringList taskIds;
    
    for (const InferenceTask& task : tasks) {
        QString taskId = submitTask(task.type, task.inputImage, task.priority, task.parameters);
        if (!taskId.isEmpty()) {
            taskIds.append(taskId);
        }
    }
    
    qDebug() << "批量提交任务完成，成功提交" << taskIds.size() << "个任务";
    return taskIds;
}

bool InferenceManager::cancelTask(const QString& taskId)
{
    QMutexLocker locker(&statsMutex_);
    
    if (!taskToWorkerMap_.contains(taskId)) {
        qDebug() << "未找到任务:" << taskId;
        return false;
    }
    
    int workerIndex = taskToWorkerMap_[taskId];
    taskToWorkerMap_.remove(taskId);
    
    if (workerIndex >= 0 && workerIndex < workers_.size()) {
        return workers_[workerIndex]->cancelTask(taskId);
    }
    
    return false;
}

void InferenceManager::clearAllTasks()
{
    QMutexLocker locker(&workersMutex_);
    
    for (auto& worker : workers_) {
        worker->clearTasks();
    }
    
    {
        QMutexLocker statsLocker(&statsMutex_);
        taskToWorkerMap_.clear();
    }
    
    qDebug() << "已清空所有任务";
}

void InferenceManager::start()
{
    if (!initialized_) {
        qDebug() << "推理管理器未初始化，无法启动";
        return;
    }
    
    if (running_) {
        qDebug() << "推理管理器已在运行";
        return;
    }
    
    running_ = true;
    paused_ = false;
    
    // 启动所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->start();
        }
    }
    
    // 启动定时器
    statsUpdateTimer_->start();
    if (performanceMonitoringEnabled_) {
        performanceCheckTimer_->start();
    }
    
    emit managerStateChanged(running_, getWorkerCount());
    qDebug() << "推理管理器已启动";
}

void InferenceManager::stop()
{
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 停止定时器
    statsUpdateTimer_->stop();
    performanceCheckTimer_->stop();
    
    // 停止所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->stop();
        }
    }
    
    emit managerStateChanged(running_, 0);
    qDebug() << "推理管理器已停止";
}

void InferenceManager::pause()
{
    if (!running_ || paused_) {
        return;
    }
    
    paused_ = true;
    
    // 暂停所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->pause();
        }
    }
    
    qDebug() << "推理管理器已暂停";
}

void InferenceManager::resume()
{
    if (!running_ || !paused_) {
        return;
    }
    
    paused_ = false;
    
    // 恢复所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->resume();
        }
    }
    
    qDebug() << "推理管理器已恢复";
}

InferenceManagerStats InferenceManager::getStats() const
{
    QMutexLocker locker(&statsMutex_);
    InferenceManagerStats stats = stats_;
    
    // 更新实时统计
    stats.totalWorkers = workers_.size();
    stats.activeWorkers = 0;
    stats.totalQueueSize = 0;
    
    for (const auto& worker : workers_) {
        auto workerStats = worker->getPerformanceStats();
        if (workerStats.isRunning) {
            stats.activeWorkers++;
        }
        stats.totalQueueSize += workerStats.queueSize;
        stats.completedTasks += workerStats.completedTasks;
        stats.failedTasks += workerStats.failedTasks;
    }
    
    return stats;
}

void InferenceManager::resetStats()
{
    QMutexLocker locker(&statsMutex_);
    stats_ = InferenceManagerStats();
    
    // 重置所有工作线程的统计
    for (auto& worker : workers_) {
        worker->resetStats();
    }
    
    qDebug() << "推理管理器统计信息已重置";
}

void InferenceManager::updateConfig(const InferenceManagerConfig& config)
{
    config_ = config;

    // 更新定时器间隔
    statsUpdateTimer_->setInterval(config_.statsUpdateInterval);

    // 更新工作线程配置
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->setMaxQueueSize(config_.maxQueueSizePerWorker);
        }
    }

    qDebug() << "推理管理器配置已更新";
}

int InferenceManager::getWorkerCount() const
{
    QMutexLocker locker(&workersMutex_);
    return workers_.size();
}

void InferenceManager::setWorkerCount(int count)
{
    if (count < 1) {
        count = 1;
    }
    if (count > QThread::idealThreadCount() * 2) {
        count = QThread::idealThreadCount() * 2;
    }

    int currentCount = getWorkerCount();

    if (count > currentCount) {
        // 添加工作线程
        for (int i = currentCount; i < count; ++i) {
            addWorker();
        }
    } else if (count < currentCount) {
        // 移除工作线程
        for (int i = currentCount; i > count; --i) {
            removeWorker();
        }
    }
}

void InferenceManager::addWorker()
{
    QMutexLocker locker(&workersMutex_);

    auto worker = std::make_shared<InferenceWorker>();
    worker->setMaxQueueSize(config_.maxQueueSizePerWorker);

    // 设置引擎
    updateWorkerEngines(worker);

    // 连接信号
    connect(worker.get(), &InferenceWorker::taskCompleted,
            this, &InferenceManager::onWorkerTaskCompleted);
    connect(worker.get(), &InferenceWorker::taskFailed,
            this, &InferenceManager::onWorkerTaskFailed);
    connect(worker.get(), &InferenceWorker::workerStateChanged,
            this, &InferenceManager::onWorkerStateChanged);

    workers_.append(worker);

    // 如果管理器正在运行，启动新的工作线程
    if (running_) {
        worker->start();
    }

    emit workerCountChanged(workers_.size());
    qDebug() << "添加工作线程，当前总数:" << workers_.size();
}

void InferenceManager::removeWorker()
{
    QMutexLocker locker(&workersMutex_);

    if (workers_.isEmpty()) {
        return;
    }

    // 移除最后一个工作线程
    auto worker = workers_.takeLast();
    worker->stop();

    emit workerCountChanged(workers_.size());
    qDebug() << "移除工作线程，当前总数:" << workers_.size();
}

void InferenceManager::enableLoadBalancing(bool enable)
{
    config_.enableLoadBalancing = enable;
    qDebug() << "负载均衡" << (enable ? "已启用" : "已禁用");
}

void InferenceManager::rebalanceTasks()
{
    if (!config_.enableLoadBalancing) {
        return;
    }

    balanceLoad();
}

void InferenceManager::enablePerformanceMonitoring(bool enable)
{
    performanceMonitoringEnabled_ = enable;

    if (enable && running_) {
        performanceCheckTimer_->start();
    } else {
        performanceCheckTimer_->stop();
    }

    qDebug() << "性能监控" << (enable ? "已启用" : "已禁用");
}

float InferenceManager::getCurrentCpuUsage() const
{
    return calculateCpuUsage();
}

float InferenceManager::getCurrentMemoryUsage() const
{
    return calculateMemoryUsage();
}

// 槽函数实现
void InferenceManager::onWorkerTaskCompleted(const InferenceResult& result)
{
    {
        QMutexLocker locker(&statsMutex_);
        taskToWorkerMap_.remove(result.taskId);
        stats_.completedTasks++;
    }

    emit taskCompleted(result);
}

void InferenceManager::onWorkerTaskFailed(const QString& taskId, const QString& errorMessage)
{
    {
        QMutexLocker locker(&statsMutex_);
        taskToWorkerMap_.remove(taskId);
        stats_.failedTasks++;
    }

    emit taskFailed(taskId, errorMessage);
}

void InferenceManager::onWorkerStateChanged(bool running, bool busy)
{
    Q_UNUSED(running)
    Q_UNUSED(busy)

    // 可以在这里实现工作线程状态变化的响应逻辑
}

void InferenceManager::onStatsUpdateTimer()
{
    collectStats();
    emit statsUpdated(getStats());
}

void InferenceManager::onPerformanceCheckTimer()
{
    checkPerformance();

    if (config_.enableAutoScaling) {
        autoScale();
    }
}
