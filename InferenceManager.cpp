#include "InferenceManager.h"
#include <QDebug>
#include <QThread>
#include <QMutexLocker>
#include <QCoreApplication>
#include <QProcess>
#include <algorithm>

#if defined(_MSC_VER)
#pragma execution_character_set("utf-8")
#endif

InferenceManager::InferenceManager(QObject* parent)
    : QObject(parent)
    , initialized_(false)
    , running_(false)
    , paused_(false)
    , tfliteEngine_(nullptr)
    , modelManager_(nullptr)
    , opencvEngine_(nullptr)
    , statsUpdateTimer_(new QTimer(this))
    , performanceCheckTimer_(new QTimer(this))
    , performanceMonitoringEnabled_(true)
{
    // 连接定时器
    connect(statsUpdateTimer_, &QTimer::timeout, this, &InferenceManager::onStatsUpdateTimer);
    connect(performanceCheckTimer_, &QTimer::timeout, this, &InferenceManager::onPerformanceCheckTimer);
    
    qDebug() << "推理管理器已创建";
}

InferenceManager::~InferenceManager()
{
    stop();
    cleanupWorkers();
    qDebug() << "推理管理器已销毁";
}

bool InferenceManager::initialize(const InferenceManagerConfig& config)
{
    if (initialized_) {
        qDebug() << "推理管理器已初始化";
        return true;
    }
    
    config_ = config;
    
    // 初始化工作线程
    initializeWorkers();
    
    // 设置定时器
    statsUpdateTimer_->setInterval(config_.statsUpdateInterval);
    performanceCheckTimer_->setInterval(5000); // 每5秒检查一次性能
    
    initialized_ = true;
    qDebug() << "推理管理器初始化完成，工作线程数:" << workers_.size();
    
    return true;
}

void InferenceManager::setTensorFlowLiteEngine(TensorFlowLiteEngine* engine)
{
    tfliteEngine_ = engine;
    
    // 更新所有工作线程的引擎
    QMutexLocker locker(&workersMutex_);
    for (auto& worker : workers_) {
        updateWorkerEngines(worker);
    }
    
    qDebug() << "TensorFlow Lite引擎已设置到推理管理器";
}

void InferenceManager::setTensorFlowLiteModelManager(TensorFlowLiteModelManager* manager)
{
    modelManager_ = manager;
    qDebug() << "TensorFlow Lite模型管理器已设置到推理管理器";
}

void InferenceManager::setOpenCVEngine(OpenCVVisionEngine* engine)
{
    opencvEngine_ = engine;
    
    // 更新所有工作线程的引擎
    QMutexLocker locker(&workersMutex_);
    for (auto& worker : workers_) {
        updateWorkerEngines(worker);
    }
    
    qDebug() << "OpenCV引擎已设置到推理管理器";
}

QString InferenceManager::submitTask(InferenceTaskType type, const QImage& image, 
                                   int priority, const QVariantMap& parameters)
{
    if (!running_) {
        qDebug() << "推理管理器未运行，无法提交任务";
        return QString();
    }
    
    // 选择最佳工作线程
    int workerIndex = selectBestWorker();
    if (workerIndex < 0 || workerIndex >= workers_.size()) {
        qDebug() << "没有可用的工作线程";
        return QString();
    }
    
    // 提交任务到选定的工作线程
    QString taskId = workers_[workerIndex]->addTask(type, image, priority, parameters);
    
    if (!taskId.isEmpty()) {
        QMutexLocker locker(&statsMutex_);
        taskToWorkerMap_[taskId] = workerIndex;
        stats_.totalTasks++;
    }
    
    return taskId;
}

QStringList InferenceManager::submitBatchTasks(const QList<InferenceTask>& tasks)
{
    QStringList taskIds;
    
    for (const InferenceTask& task : tasks) {
        QString taskId = submitTask(task.type, task.inputImage, task.priority, task.parameters);
        if (!taskId.isEmpty()) {
            taskIds.append(taskId);
        }
    }
    
    qDebug() << "批量提交任务完成，成功提交" << taskIds.size() << "个任务";
    return taskIds;
}

bool InferenceManager::cancelTask(const QString& taskId)
{
    QMutexLocker locker(&statsMutex_);
    
    if (!taskToWorkerMap_.contains(taskId)) {
        qDebug() << "未找到任务:" << taskId;
        return false;
    }
    
    int workerIndex = taskToWorkerMap_[taskId];
    taskToWorkerMap_.remove(taskId);
    
    if (workerIndex >= 0 && workerIndex < workers_.size()) {
        return workers_[workerIndex]->cancelTask(taskId);
    }
    
    return false;
}

void InferenceManager::clearAllTasks()
{
    QMutexLocker locker(&workersMutex_);
    
    for (auto& worker : workers_) {
        worker->clearTasks();
    }
    
    {
        QMutexLocker statsLocker(&statsMutex_);
        taskToWorkerMap_.clear();
    }
    
    qDebug() << "已清空所有任务";
}

void InferenceManager::start()
{
    if (!initialized_) {
        qDebug() << "推理管理器未初始化，无法启动";
        return;
    }
    
    if (running_) {
        qDebug() << "推理管理器已在运行";
        return;
    }
    
    running_ = true;
    paused_ = false;
    
    // 启动所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->start();
        }
    }
    
    // 启动定时器
    statsUpdateTimer_->start();
    if (performanceMonitoringEnabled_) {
        performanceCheckTimer_->start();
    }
    
    emit managerStateChanged(running_, getWorkerCount());
    qDebug() << "推理管理器已启动";
}

void InferenceManager::stop()
{
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 停止定时器
    statsUpdateTimer_->stop();
    performanceCheckTimer_->stop();
    
    // 停止所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->stop();
        }
    }
    
    emit managerStateChanged(running_, 0);
    qDebug() << "推理管理器已停止";
}

void InferenceManager::pause()
{
    if (!running_ || paused_) {
        return;
    }
    
    paused_ = true;
    
    // 暂停所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->pause();
        }
    }
    
    qDebug() << "推理管理器已暂停";
}

void InferenceManager::resume()
{
    if (!running_ || !paused_) {
        return;
    }
    
    paused_ = false;
    
    // 恢复所有工作线程
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->resume();
        }
    }
    
    qDebug() << "推理管理器已恢复";
}

InferenceManagerStats InferenceManager::getStats() const
{
    QMutexLocker locker(&statsMutex_);
    InferenceManagerStats stats = stats_;
    
    // 更新实时统计
    stats.totalWorkers = workers_.size();
    stats.activeWorkers = 0;
    stats.totalQueueSize = 0;
    
    for (const auto& worker : workers_) {
        auto workerStats = worker->getPerformanceStats();
        if (workerStats.isRunning) {
            stats.activeWorkers++;
        }
        stats.totalQueueSize += workerStats.queueSize;
        stats.completedTasks += workerStats.completedTasks;
        stats.failedTasks += workerStats.failedTasks;
    }
    
    return stats;
}

void InferenceManager::resetStats()
{
    QMutexLocker locker(&statsMutex_);
    stats_ = InferenceManagerStats();
    
    // 重置所有工作线程的统计
    for (auto& worker : workers_) {
        worker->resetStats();
    }
    
    qDebug() << "推理管理器统计信息已重置";
}

void InferenceManager::updateConfig(const InferenceManagerConfig& config)
{
    config_ = config;

    // 更新定时器间隔
    statsUpdateTimer_->setInterval(config_.statsUpdateInterval);

    // 更新工作线程配置
    {
        QMutexLocker locker(&workersMutex_);
        for (auto& worker : workers_) {
            worker->setMaxQueueSize(config_.maxQueueSizePerWorker);
        }
    }

    qDebug() << "推理管理器配置已更新";
}

int InferenceManager::getWorkerCount() const
{
    QMutexLocker locker(&workersMutex_);
    return workers_.size();
}

void InferenceManager::setWorkerCount(int count)
{
    if (count < 1) {
        count = 1;
    }
    if (count > QThread::idealThreadCount() * 2) {
        count = QThread::idealThreadCount() * 2;
    }

    int currentCount = getWorkerCount();

    if (count > currentCount) {
        // 添加工作线程
        for (int i = currentCount; i < count; ++i) {
            addWorker();
        }
    } else if (count < currentCount) {
        // 移除工作线程
        for (int i = currentCount; i > count; --i) {
            removeWorker();
        }
    }
}

void InferenceManager::addWorker()
{
    QMutexLocker locker(&workersMutex_);

    auto worker = std::make_shared<InferenceWorker>();
    worker->setMaxQueueSize(config_.maxQueueSizePerWorker);

    // 设置引擎
    updateWorkerEngines(worker);

    // 连接信号
    connect(worker.get(), &InferenceWorker::taskCompleted,
            this, &InferenceManager::onWorkerTaskCompleted);
    connect(worker.get(), &InferenceWorker::taskFailed,
            this, &InferenceManager::onWorkerTaskFailed);
    connect(worker.get(), &InferenceWorker::workerStateChanged,
            this, &InferenceManager::onWorkerStateChanged);

    workers_.append(worker);

    // 如果管理器正在运行，启动新的工作线程
    if (running_) {
        worker->start();
    }

    emit workerCountChanged(workers_.size());
    qDebug() << "添加工作线程，当前总数:" << workers_.size();
}

void InferenceManager::removeWorker()
{
    QMutexLocker locker(&workersMutex_);

    if (workers_.isEmpty()) {
        return;
    }

    // 移除最后一个工作线程
    auto worker = workers_.takeLast();
    worker->stop();

    emit workerCountChanged(workers_.size());
    qDebug() << "移除工作线程，当前总数:" << workers_.size();
}

void InferenceManager::enableLoadBalancing(bool enable)
{
    config_.enableLoadBalancing = enable;
    qDebug() << "负载均衡" << (enable ? "已启用" : "已禁用");
}

void InferenceManager::rebalanceTasks()
{
    if (!config_.enableLoadBalancing) {
        return;
    }

    balanceLoad();
}

void InferenceManager::enablePerformanceMonitoring(bool enable)
{
    performanceMonitoringEnabled_ = enable;

    if (enable && running_) {
        performanceCheckTimer_->start();
    } else {
        performanceCheckTimer_->stop();
    }

    qDebug() << "性能监控" << (enable ? "已启用" : "已禁用");
}

float InferenceManager::getCurrentCpuUsage() const
{
    return calculateCpuUsage();
}

float InferenceManager::getCurrentMemoryUsage() const
{
    return calculateMemoryUsage();
}

// 槽函数实现
void InferenceManager::onWorkerTaskCompleted(const InferenceResult& result)
{
    {
        QMutexLocker locker(&statsMutex_);
        taskToWorkerMap_.remove(result.taskId);
        stats_.completedTasks++;
    }

    emit taskCompleted(result);
}

void InferenceManager::onWorkerTaskFailed(const QString& taskId, const QString& errorMessage)
{
    {
        QMutexLocker locker(&statsMutex_);
        taskToWorkerMap_.remove(taskId);
        stats_.failedTasks++;
    }

    emit taskFailed(taskId, errorMessage);
}

void InferenceManager::onWorkerStateChanged(bool running, bool busy)
{
    Q_UNUSED(running)
    Q_UNUSED(busy)

    // 可以在这里实现工作线程状态变化的响应逻辑
}

void InferenceManager::onStatsUpdateTimer()
{
    collectStats();
    emit statsUpdated(getStats());
}

void InferenceManager::onPerformanceCheckTimer()
{
    checkPerformance();

    if (config_.enableAutoScaling) {
        autoScale();
    }
}

// 私有方法实现
void InferenceManager::initializeWorkers()
{
    QMutexLocker locker(&workersMutex_);

    // 创建初始工作线程
    int initialWorkerCount = std::max(1, std::min(config_.maxWorkerThreads, QThread::idealThreadCount()));

    for (int i = 0; i < initialWorkerCount; ++i) {
        auto worker = std::make_shared<InferenceWorker>();
        worker->setMaxQueueSize(config_.maxQueueSizePerWorker);

        // 设置引擎
        updateWorkerEngines(worker);

        // 连接信号
        connect(worker.get(), &InferenceWorker::taskCompleted,
                this, &InferenceManager::onWorkerTaskCompleted);
        connect(worker.get(), &InferenceWorker::taskFailed,
                this, &InferenceManager::onWorkerTaskFailed);
        connect(worker.get(), &InferenceWorker::workerStateChanged,
                this, &InferenceManager::onWorkerStateChanged);

        workers_.append(worker);
    }

    qDebug() << "初始化了" << workers_.size() << "个工作线程";
}

void InferenceManager::cleanupWorkers()
{
    QMutexLocker locker(&workersMutex_);

    for (auto& worker : workers_) {
        worker->stop();
    }

    workers_.clear();
    qDebug() << "清理了所有工作线程";
}

int InferenceManager::selectBestWorker() const
{
    QMutexLocker locker(&workersMutex_);

    if (workers_.isEmpty()) {
        return -1;
    }

    if (!config_.enableLoadBalancing) {
        // 简单轮询
        static int lastWorkerIndex = 0;
        lastWorkerIndex = (lastWorkerIndex + 1) % workers_.size();
        return lastWorkerIndex;
    }

    // 负载均衡：选择队列最小的工作线程
    int bestWorkerIndex = 0;
    int minQueueSize = workers_[0]->getQueueSize();

    for (int i = 1; i < workers_.size(); ++i) {
        int queueSize = workers_[i]->getQueueSize();
        if (queueSize < minQueueSize) {
            minQueueSize = queueSize;
            bestWorkerIndex = i;
        }
    }

    return bestWorkerIndex;
}

void InferenceManager::updateWorkerEngines(std::shared_ptr<InferenceWorker> worker)
{
    if (tfliteEngine_) {
        worker->setTensorFlowLiteEngine(tfliteEngine_);
    }
    if (opencvEngine_) {
        worker->setOpenCVEngine(opencvEngine_);
    }
}

void InferenceManager::collectStats()
{
    QMutexLocker locker(&statsMutex_);

    // 收集系统性能数据
    stats_.systemCpuUsage = calculateCpuUsage();
    stats_.memoryUsage = calculateMemoryUsage();

    // 更新性能历史
    updatePerformanceHistory(stats_.systemCpuUsage, stats_.memoryUsage);

    // 收集工作线程统计
    float totalProcessingTime = 0.0f;
    int totalCompletedTasks = 0;

    for (const auto& worker : workers_) {
        auto workerStats = worker->getPerformanceStats();
        totalProcessingTime += workerStats.averageProcessingTime * workerStats.completedTasks;
        totalCompletedTasks += workerStats.completedTasks;
    }

    if (totalCompletedTasks > 0) {
        stats_.averageProcessingTime = totalProcessingTime / totalCompletedTasks;
    }
}

void InferenceManager::checkPerformance()
{
    float cpuUsage = calculateCpuUsage();
    float memoryUsage = calculateMemoryUsage();

    // 检查CPU使用率
    if (cpuUsage > config_.cpuThresholdHigh) {
        emit performanceWarning("CPU使用率过高", cpuUsage);
    }

    // 检查内存使用率
    if (memoryUsage > 90.0f) {
        emit performanceWarning("内存使用率过高", memoryUsage);
    }

    // 检查队列积压
    int totalQueueSize = 0;
    for (const auto& worker : workers_) {
        totalQueueSize += worker->getQueueSize();
    }

    if (totalQueueSize > config_.maxQueueSizePerWorker * workers_.size() * 0.8f) {
        emit performanceWarning("任务队列积压严重", totalQueueSize);
    }
}

void InferenceManager::autoScale()
{
    if (shouldAddWorker()) {
        if (workers_.size() < config_.maxWorkerThreads) {
            addWorker();
            qDebug() << "自动扩容：添加工作线程";
        }
    } else if (shouldRemoveWorker()) {
        if (workers_.size() > 1) {
            removeWorker();
            qDebug() << "自动缩容：移除工作线程";
        }
    }
}

bool InferenceManager::shouldAddWorker() const
{
    // 检查CPU使用率
    if (!cpuUsageHistory_.isEmpty()) {
        float avgCpuUsage = 0.0f;
        for (float usage : cpuUsageHistory_) {
            avgCpuUsage += usage;
        }
        avgCpuUsage /= cpuUsageHistory_.size();

        if (avgCpuUsage > config_.cpuThresholdHigh) {
            return false; // CPU已经很高，不适合添加更多线程
        }
    }

    // 检查队列积压
    int totalQueueSize = 0;
    for (const auto& worker : workers_) {
        totalQueueSize += worker->getQueueSize();
    }

    // 如果平均队列长度超过阈值，考虑添加工作线程
    float avgQueueSize = static_cast<float>(totalQueueSize) / workers_.size();
    return avgQueueSize > config_.maxQueueSizePerWorker * 0.7f;
}

bool InferenceManager::shouldRemoveWorker() const
{
    // 检查队列是否空闲
    int totalQueueSize = 0;
    int idleWorkers = 0;

    for (const auto& worker : workers_) {
        int queueSize = worker->getQueueSize();
        totalQueueSize += queueSize;

        if (queueSize == 0 && !worker->isBusy()) {
            idleWorkers++;
        }
    }

    // 如果有多个空闲工作线程，考虑移除一个
    return idleWorkers > 1 && totalQueueSize < workers_.size() * 2;
}

void InferenceManager::balanceLoad()
{
    QMutexLocker locker(&workersMutex_);

    if (workers_.size() < 2) {
        return; // 只有一个工作线程，无需负载均衡
    }

    // 计算每个工作线程的负载
    QList<int> loads;
    for (const auto& worker : workers_) {
        loads.append(worker->getQueueSize());
    }

    // 找到最高和最低负载
    int maxLoad = *std::max_element(loads.begin(), loads.end());
    int minLoad = *std::min_element(loads.begin(), loads.end());

    // 如果负载差异不大，不需要重新平衡
    if (maxLoad - minLoad <= 2) {
        return;
    }

    qDebug() << "执行负载均衡，最大负载:" << maxLoad << "最小负载:" << minLoad;

    // 注意：实际的任务迁移比较复杂，这里只是示例
    // 在实际实现中，可能需要更复杂的任务迁移策略
}

int InferenceManager::getWorkerLoad(int workerIndex) const
{
    if (workerIndex < 0 || workerIndex >= workers_.size()) {
        return 0;
    }

    return workers_[workerIndex]->getQueueSize();
}

float InferenceManager::calculateCpuUsage() const
{
    // 简化的CPU使用率计算
    // 在实际应用中，可能需要使用平台特定的API

#ifdef Q_OS_WIN
    // Windows平台的CPU使用率计算
    QProcess process;
    process.start("wmic", QStringList() << "cpu" << "get" << "loadpercentage" << "/value");
    process.waitForFinished(1000);

    QString output = process.readAllStandardOutput();
    QRegExp rx("LoadPercentage=(\\d+)");
    if (rx.indexIn(output) != -1) {
        return rx.cap(1).toFloat();
    }
#elif defined(Q_OS_LINUX)
    // Linux平台的CPU使用率计算
    QFile file("/proc/loadavg");
    if (file.open(QIODevice::ReadOnly)) {
        QString content = file.readAll();
        QStringList parts = content.split(' ');
        if (!parts.isEmpty()) {
            float load = parts[0].toFloat();
            return std::min(load * 100.0f / QThread::idealThreadCount(), 100.0f);
        }
    }
#endif

    // 默认返回一个估算值
    return 50.0f;
}

float InferenceManager::calculateMemoryUsage() const
{
    // 简化的内存使用率计算
    // 在实际应用中，可能需要使用平台特定的API

#ifdef Q_OS_WIN
    // Windows平台的内存使用率计算
    QProcess process;
    process.start("wmic", QStringList() << "OS" << "get" << "TotalVisibleMemorySize,FreePhysicalMemory" << "/value");
    process.waitForFinished(1000);

    QString output = process.readAllStandardOutput();
    QRegExp totalRx("TotalVisibleMemorySize=(\\d+)");
    QRegExp freeRx("FreePhysicalMemory=(\\d+)");

    if (totalRx.indexIn(output) != -1 && freeRx.indexIn(output) != -1) {
        qint64 total = totalRx.cap(1).toLongLong();
        qint64 free = freeRx.cap(1).toLongLong();
        if (total > 0) {
            return ((total - free) * 100.0f) / total;
        }
    }
#elif defined(Q_OS_LINUX)
    // Linux平台的内存使用率计算
    QFile file("/proc/meminfo");
    if (file.open(QIODevice::ReadOnly)) {
        QString content = file.readAll();
        QStringList lines = content.split('\n');

        qint64 memTotal = 0, memFree = 0, buffers = 0, cached = 0;

        for (const QString& line : lines) {
            if (line.startsWith("MemTotal:")) {
                memTotal = line.split(QRegExp("\\s+"))[1].toLongLong();
            } else if (line.startsWith("MemFree:")) {
                memFree = line.split(QRegExp("\\s+"))[1].toLongLong();
            } else if (line.startsWith("Buffers:")) {
                buffers = line.split(QRegExp("\\s+"))[1].toLongLong();
            } else if (line.startsWith("Cached:")) {
                cached = line.split(QRegExp("\\s+"))[1].toLongLong();
            }
        }

        if (memTotal > 0) {
            qint64 memUsed = memTotal - memFree - buffers - cached;
            return (memUsed * 100.0f) / memTotal;
        }
    }
#endif

    // 默认返回一个估算值
    return 60.0f;
}

void InferenceManager::updatePerformanceHistory(float cpuUsage, float memoryUsage)
{
    const int maxHistorySize = 60; // 保留最近60个数据点

    cpuUsageHistory_.append(cpuUsage);
    if (cpuUsageHistory_.size() > maxHistorySize) {
        cpuUsageHistory_.removeFirst();
    }

    memoryUsageHistory_.append(memoryUsage);
    if (memoryUsageHistory_.size() > maxHistorySize) {
        memoryUsageHistory_.removeFirst();
    }
}
