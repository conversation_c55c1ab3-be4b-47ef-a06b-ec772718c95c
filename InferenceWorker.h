#pragma once

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QQueue>
#include <QImage>
#include <QUuid>
#include <QElapsedTimer>
#include <memory>

#include "TensorFlowLiteEngine.h"
#include "OpenCVVisionEngine.h"

/**
 * @brief 推理任务类型
 */
enum class InferenceTaskType {
    TensorFlowLite_Classification,  // TensorFlow Lite 图像分类
    TensorFlowLite_Detection,       // TensorFlow Lite 目标检测
    OpenCV_Detection,               // OpenCV 目标检测
    OpenCV_FaceDetection,          // OpenCV 人脸检测
    OpenCV_MotionDetection,        // OpenCV 运动检测
    OpenCV_EdgeDetection           // OpenCV 边缘检测
};

/**
 * @brief 推理任务
 */
struct InferenceTask {
    QString taskId;                 // 任务ID
    InferenceTaskType type;         // 任务类型
    QImage inputImage;              // 输入图像
    QVariantMap parameters;         // 任务参数
    int priority;                   // 优先级 (0-10, 10最高)
    qint64 timestamp;              // 创建时间戳
    
    InferenceTask() : priority(5), timestamp(0) {}
    
    InferenceTask(const QString& id, InferenceTaskType t, const QImage& img, int prio = 5)
        : taskId(id), type(t), inputImage(img), priority(prio)
        , timestamp(QDateTime::currentMSecsSinceEpoch()) {}
};

/**
 * @brief 推理结果
 */
struct InferenceResult {
    QString taskId;                 // 对应的任务ID
    InferenceTaskType type;         // 任务类型
    bool success;                   // 是否成功
    QString errorMessage;           // 错误信息
    float processingTime;           // 处理耗时(ms)
    qint64 timestamp;              // 完成时间戳
    
    // TensorFlow Lite结果
    TFLiteInferenceResult tfliteResult;
    
    // OpenCV结果
    QList<Detection> opencvDetections;
    QImage processedImage;
    
    InferenceResult() : success(false), processingTime(0.0f), timestamp(0) {}
};

/**
 * @brief 推理工作线程
 * 
 * 在后台线程中执行模型推理，避免阻塞UI线程
 */
class InferenceWorker : public QObject {
    Q_OBJECT

public:
    explicit InferenceWorker(QObject* parent = nullptr);
    ~InferenceWorker();

    /**
     * @brief 设置TensorFlow Lite引擎
     */
    void setTensorFlowLiteEngine(TensorFlowLiteEngine* engine);
    
    /**
     * @brief 设置OpenCV引擎
     */
    void setOpenCVEngine(OpenCVVisionEngine* engine);

    /**
     * @brief 添加推理任务
     */
    QString addTask(InferenceTaskType type, const QImage& image, 
                   int priority = 5, const QVariantMap& parameters = QVariantMap());

    /**
     * @brief 取消任务
     */
    bool cancelTask(const QString& taskId);

    /**
     * @brief 清空任务队列
     */
    void clearTasks();

    /**
     * @brief 获取队列中的任务数量
     */
    int getQueueSize() const;

    /**
     * @brief 获取工作线程状态
     */
    bool isRunning() const { return running_; }
    bool isBusy() const { return busy_; }

    /**
     * @brief 设置最大队列大小
     */
    void setMaxQueueSize(int maxSize) { maxQueueSize_ = maxSize; }

    /**
     * @brief 获取性能统计
     */
    struct PerformanceStats {
        int totalTasks = 0;
        int completedTasks = 0;
        int failedTasks = 0;
        float averageProcessingTime = 0.0f;
        float minProcessingTime = 0.0f;
        float maxProcessingTime = 0.0f;
        int queueSize = 0;
        bool isRunning = false;
        bool isBusy = false;
    };

    PerformanceStats getPerformanceStats() const;
    void resetStats();

public slots:
    /**
     * @brief 启动工作线程
     */
    void start();

    /**
     * @brief 停止工作线程
     */
    void stop();

    /**
     * @brief 暂停处理
     */
    void pause();

    /**
     * @brief 恢复处理
     */
    void resume();

signals:
    /**
     * @brief 任务完成信号
     */
    void taskCompleted(const InferenceResult& result);

    /**
     * @brief 任务失败信号
     */
    void taskFailed(const QString& taskId, const QString& errorMessage);

    /**
     * @brief 队列状态变化信号
     */
    void queueSizeChanged(int size);

    /**
     * @brief 性能统计更新信号
     */
    void performanceStatsUpdated(const PerformanceStats& stats);

    /**
     * @brief 工作线程状态变化信号
     */
    void workerStateChanged(bool running, bool busy);

private slots:
    void processNextTask();

private:
    // 引擎
    TensorFlowLiteEngine* tfliteEngine_;
    OpenCVVisionEngine* opencvEngine_;

    // 线程控制
    QThread* workerThread_;
    mutable QMutex mutex_;
    QWaitCondition condition_;
    bool running_;
    bool paused_;
    bool busy_;
    bool stopRequested_;

    // 任务队列
    QQueue<InferenceTask> taskQueue_;
    int maxQueueSize_;

    // 性能统计
    mutable PerformanceStats stats_;

    // 私有方法
    void processTask(const InferenceTask& task);
    InferenceResult processTensorFlowLiteTask(const InferenceTask& task);
    InferenceResult processOpenCVTask(const InferenceTask& task);
    void updateStats(float processingTime, bool success);
    void emitStatsUpdate();
    
    // 任务优先级比较
    static bool taskPriorityCompare(const InferenceTask& a, const InferenceTask& b);
    void sortTaskQueue();
};
