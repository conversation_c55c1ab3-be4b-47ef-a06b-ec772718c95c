﻿cmake_minimum_required(VERSION 3.16)

if (MSVC)
    add_compile_options("/utf-8")
endif()

project(AIVersion VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置Qt6路径
set(CMAKE_PREFIX_PATH "D:/QT/6.9.1/msvc2022_64")
set(Qt6_DIR "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6")

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia MultimediaWidgets Network)

# OpenCV配置
set(OpenCV_DIR "D:/opencv/build")
find_package(OpenCV REQUIRED)

message(STATUS "OpenCV library status:")
message(STATUS "    version: ${OpenCV_VERSION}")
message(STATUS "    libraries: ${OpenCV_LIBS}")
message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")

# TensorFlow Lite配置
option(ENABLE_TENSORFLOW_LITE "Enable TensorFlow Lite support" ON)

if(ENABLE_TENSORFLOW_LITE)
    # 尝试查找TensorFlow Lite
    find_path(TENSORFLOW_LITE_INCLUDE_DIR
        NAMES tensorflow/lite/interpreter.h
        PATHS
            "D:/tensorflow_lite/include"
            "C:/tensorflow_lite/include"
            "/usr/local/include"
            "/usr/include"
        DOC "TensorFlow Lite include directory"
    )

    find_library(TENSORFLOW_LITE_LIB
        NAMES tensorflowlite tensorflow-lite
        PATHS
            "D:/tensorflow_lite/lib"
            "C:/tensorflow_lite/lib"
            "/usr/local/lib"
            "/usr/lib"
        DOC "TensorFlow Lite library"
    )

    if(TENSORFLOW_LITE_INCLUDE_DIR AND TENSORFLOW_LITE_LIB)
        message(STATUS "TensorFlow Lite found:")
        message(STATUS "    include: ${TENSORFLOW_LITE_INCLUDE_DIR}")
        message(STATUS "    library: ${TENSORFLOW_LITE_LIB}")
        add_definitions(-DTENSORFLOW_LITE_AVAILABLE)
        set(TENSORFLOW_LITE_FOUND TRUE)
    else()
        message(WARNING "TensorFlow Lite not found. TensorFlow Lite features will be disabled.")
        message(STATUS "To enable TensorFlow Lite, please:")
        message(STATUS "1. Download TensorFlow Lite C++ library")
        message(STATUS "2. Extract to D:/tensorflow_lite/ or C:/tensorflow_lite/")
        message(STATUS "3. Ensure the following structure:")
        message(STATUS "   tensorflow_lite/")
        message(STATUS "   ├── include/tensorflow/lite/")
        message(STATUS "   └── lib/tensorflowlite.lib (or .a)")
        set(TENSORFLOW_LITE_FOUND FALSE)
    endif()
else()
    message(STATUS "TensorFlow Lite support disabled")
    set(TENSORFLOW_LITE_FOUND FALSE)
endif()

# 包含目录
include_directories(${OpenCV_INCLUDE_DIRS})
if(TENSORFLOW_LITE_FOUND)
    include_directories(${TENSORFLOW_LITE_INCLUDE_DIR})
endif()

# 项目源文件
set(PROJECT_SOURCES
    main.cpp
    mainwindow.cpp
    mainwindow.h
    OpenCVVisionEngine.cpp
    OpenCVVisionEngine.h
    VideoFrameCapture.cpp
    VideoFrameCapture.h
    GeminiAPIManager.cpp
    GeminiAPIManager.h
    DetectionOverlay.cpp
    DetectionOverlay.h
    TensorFlowLiteEngine.cpp
    TensorFlowLiteEngine.h
    TensorFlowLiteModelManager.cpp
    TensorFlowLiteModelManager.h
    InferenceWorker.cpp
    InferenceWorker.h
    InferenceManager.cpp
    InferenceManager.h
    DatabaseManager.cpp
    DatabaseManager.h
)

if(MSVC)
    # 这个选项 /utf-8 告诉 MSVC 编译器：
    # 1. 源文件(.cpp, .h) 是用 UTF-8 编码保存的。
    # 2. 程序运行时，窄字符串字面量(如 "你好") 也应该被解释为 UTF-8。
    add_compile_options(/utf-8)
endif()

# 创建可执行文件
qt_add_executable(AIVersion
    MANUAL_FINALIZATION
    ${PROJECT_SOURCES}
)

# 链接库
target_link_libraries(AIVersion PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Multimedia
    Qt6::MultimediaWidgets
    Qt6::Network
    Qt6::Sql
    ${OpenCV_LIBS}
)

# 添加TensorFlow Lite库（如果找到）
if(TENSORFLOW_LITE_FOUND)
    target_link_libraries(AIVersion PRIVATE ${TENSORFLOW_LITE_LIB})
    message(STATUS "TensorFlow Lite library linked successfully")
endif()

# 设置输出目录
set_target_properties(AIVersion PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 设置UTF-8编码
if(MSVC)
    add_compile_options("/utf-8")
else()
    add_compile_options("-finput-charset=UTF-8")
endif()

qt_finalize_executable(AIVersion)
