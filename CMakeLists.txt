﻿cmake_minimum_required(VERSION 3.16)

if (MSVC)
    add_compile_options("/utf-8")
endif()

project(AIVersion VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置Qt6路径
set(CMAKE_PREFIX_PATH "D:/QT/6.9.1/msvc2022_64")
set(Qt6_DIR "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6")

# 查找Qt6组件
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia MultimediaWidgets Network)

# OpenCV配置
set(OpenCV_DIR "D:/opencv/build")
find_package(OpenCV REQUIRED)

message(STATUS "OpenCV library status:")
message(STATUS "    version: ${OpenCV_VERSION}")
message(STATUS "    libraries: ${OpenCV_LIBS}")
message(STATUS "    include path: ${OpenCV_INCLUDE_DIRS}")

# 包含目录
include_directories(${OpenCV_INCLUDE_DIRS})

# 项目源文件
set(PROJECT_SOURCES
    main.cpp
    mainwindow.cpp
    mainwindow.h
    OpenCVVisionEngine.cpp
    OpenCVVisionEngine.h
    VideoFrameCapture.cpp
    VideoFrameCapture.h
    GeminiAPIManager.cpp
    GeminiAPIManager.h
    DetectionOverlay.cpp
    DetectionOverlay.h
)

if(MSVC)
    # 这个选项 /utf-8 告诉 MSVC 编译器：
    # 1. 源文件(.cpp, .h) 是用 UTF-8 编码保存的。
    # 2. 程序运行时，窄字符串字面量(如 "你好") 也应该被解释为 UTF-8。
    add_compile_options(/utf-8)
endif()

# 创建可执行文件
qt_add_executable(AIVersion
    MANUAL_FINALIZATION
    ${PROJECT_SOURCES}
)

# 链接库
target_link_libraries(AIVersion PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Multimedia
    Qt6::MultimediaWidgets
    Qt6::Network
    ${OpenCV_LIBS}
)

# 设置输出目录
set_target_properties(AIVersion PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 设置UTF-8编码
if(MSVC)
    add_compile_options("/utf-8")
else()
    add_compile_options("-finput-charset=UTF-8")
endif()

qt_finalize_executable(AIVersion)
