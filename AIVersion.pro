# ---- 基础配置 ----
QT       += core gui
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11
DEFINES += QT_DEPRECATED_WARNINGS

# ---- MSVC 编译器中文编码配置 (解决乱码问题的关键！) ----
msvc {
    QMAKE_CXXFLAGS += /utf-8
    QMAKE_CFLAGS += /utf-8
}

# ---- 添加你的源文件 (.cpp) ----
# 把你项目里所有的 .cpp 文件名都加到这里
SOURCES += \
    main.cpp \
    mainwindow.cpp \
    DetectionOverlay.cpp \
    GeminiAPIManager.cpp \
    OpenCVVisionEngine.cpp \
    VideoFrameCapture.cpp

# ---- 添加你的头文件 (.h) ----
# 把你项目里所有的 .h 文件名都加到这里 (如果文件名和 .cpp 对应，可以推断出来)
HEADERS += \
    mainwindow.h \
    DetectionOverlay.h \
    GeminiAPIManager.h \
    OpenCVVisionEngine.h \
    VideoFrameCapture.h

# ---- 添加你的界面文件 (.ui) ----
# 如果你有 .ui 文件，也加进来
FORMS += \
    mainwindow.ui

# 如果你用了资源文件（比如图片），也加上
# RESOURCES += resources.qrc
