#############################################################################
# Makefile for building: AIVersion
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\..\AIVersion.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_DEPRECATED_WARNINGS -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_QML_DEBUG -DQT_NO_DEBUG -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zc:strictStrings -O2 -MD -utf-8 -W3 -w44456 -w44457 -w44458 $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -utf-8 -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44456 -w44457 -w44458 -wd4577 -wd4467 -EHsc $(DEFINES)
INCPATH       = -I..\..\..\AIVersion -I. -I..\..\..\..\opencv\build\include -I..\..\..\AIVersion -I..\..\..\..\QT\6.9.1\msvc2022_64\include -I..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets -I..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets -I..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia -I..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui -I..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork -I..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore -I..\release\moc -I/include -I..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /OPT:REF /OPT:ICF /INCREMENTAL:NO /SUBSYSTEM:CONSOLE "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'" /VERSION:1.0
LIBS          = /LIBPATH:D:\opencv\build\x64\vc16\lib D:\opencv\build\x64\vc16\lib\opencv_world4110.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgets.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Multimedia.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib "D:\QT  pros\AIVersion\build\release\obj\version.res"   
QMAKE         = D:\QT\6.9.1\msvc2022_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\QT\6.9.1\msvc2022_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\QT\6.9.1\msvc2022_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = "D:\QT  pros\AIVersion\build\release\obj\version.res"
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = ..\release\obj

####### Files

SOURCES       = ..\..\main.cpp \
		..\..\mainwindow.cpp \
		..\..\OpenCVVisionEngine.cpp \
		..\..\VideoFrameCapture.cpp \
		..\..\GeminiAPIManager.cpp \
		..\..\DetectionOverlay.cpp ..\release\moc\moc_mainwindow.cpp \
		..\release\moc\moc_OpenCVVisionEngine.cpp \
		..\release\moc\moc_VideoFrameCapture.cpp \
		..\release\moc\moc_GeminiAPIManager.cpp \
		..\release\moc\moc_DetectionOverlay.cpp
OBJECTS       = ..\release\obj\main.obj \
		..\release\obj\mainwindow.obj \
		..\release\obj\OpenCVVisionEngine.obj \
		..\release\obj\VideoFrameCapture.obj \
		..\release\obj\GeminiAPIManager.obj \
		..\release\obj\DetectionOverlay.obj \
		..\release\obj\moc_mainwindow.obj \
		..\release\obj\moc_OpenCVVisionEngine.obj \
		..\release\obj\moc_VideoFrameCapture.obj \
		..\release\obj\moc_GeminiAPIManager.obj \
		..\release\obj\moc_DetectionOverlay.obj

DIST          =  ..\..\mainwindow.h \
		..\..\OpenCVVisionEngine.h \
		..\..\VideoFrameCapture.h \
		..\..\GeminiAPIManager.h \
		..\..\DetectionOverlay.h ..\..\main.cpp \
		..\..\mainwindow.cpp \
		..\..\OpenCVVisionEngine.cpp \
		..\..\VideoFrameCapture.cpp \
		..\..\GeminiAPIManager.cpp \
		..\..\DetectionOverlay.cpp
QMAKE_TARGET  = AIVersion
DESTDIR        = ..\release\ #avoid trailing-slash linebreak
TARGET         = AIVersion.exe
DESTDIR_TARGET = ..\release\AIVersion.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{..\release\moc}.cpp{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\release\moc}.cc{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\release\moc}.cxx{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\release\moc}.c{..\release\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{.}.cpp{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{.}.cc{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{.}.cxx{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{.}.c{..\release\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{"D:/QT  pros/AIVersion/build/release/ui"}.cpp{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{"D:/QT  pros/AIVersion/build/release/ui"}.cc{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{"D:/QT  pros/AIVersion/build/release/ui"}.cxx{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{"D:/QT  pros/AIVersion/build/release/ui"}.c{..\release\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\..}.cpp{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\..}.cc{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\..}.cxx{..\release\obj\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

{..\..}.c{..\release\obj\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fo..\release\obj\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Release  ..\release\AIVersion.exe

..\release\AIVersion.exe: D:\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgets.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Multimedia.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Gui.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Network.lib D:\QT\6.9.1\msvc2022_64\lib\Qt6Core.lib $(OBJECTS) "D:\QT  pros\AIVersion\build\release\obj\version.res"
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
..\release\obj\main.obj ..\release\obj\mainwindow.obj ..\release\obj\OpenCVVisionEngine.obj ..\release\obj\VideoFrameCapture.obj ..\release\obj\GeminiAPIManager.obj ..\release\obj\DetectionOverlay.obj ..\release\obj\moc_mainwindow.obj ..\release\obj\moc_OpenCVVisionEngine.obj ..\release\obj\moc_VideoFrameCapture.obj ..\release\obj\moc_GeminiAPIManager.obj ..\release\obj\moc_DetectionOverlay.obj
$(LIBS)
<<

"D:\QT  pros\AIVersion\build\release\obj\version.res": ..\..\version.rc
	rc /NOLOGO $(DEFINES) -fo "D:\QT  pros\AIVersion\build\release\obj\version.res" ..\..\version.rc

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ..\..\AIVersion.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) AIVersion.zip $(SOURCES) $(DIST) ..\..\AIVersion.pro ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\spec_pre.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\common\windows-desktop.conf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\win32\windows_vulkan_sdk.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\common\windows-vulkan.conf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-desktop.conf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\qconfig.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_freetype.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libjpeg.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_libpng.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_ext_openxr_loader.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_charts_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_chartsqml_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_concurrent_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_core_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_dbus_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designer_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_designercomponents_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_entrypoint_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_example_icons_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_fb_support_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_freetype_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_gui_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_harfbuzz_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_help_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_jpeg_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsanimation_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsplatform_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssettings_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labssharedimage_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_linguist.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimedia_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediaquick_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_network_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_opengl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_openglwidgets_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_packetprotocol_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_png_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_printsupport_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatch_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qml_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlcore_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldebug_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmldom_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlformat_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlintegration_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlls_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmeta_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlmodels_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltest_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3d_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3deffects_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dutils_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick3dxr_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quick_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickeffects_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicklayouts_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickparticles_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickshapes_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimeline_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_quickwidgets_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_shadertools_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_spatialaudio_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_sql_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svg_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_svgwidgets_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testinternals_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_testlib_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_tools_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uiplugin.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_uitools_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_widgets_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_xml_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\modules\qt_lib_zlib_private.pri ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\qt_functions.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\qt_config.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\win32-msvc\qmake.conf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\spec_post.prf .qmake.stash ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\common\msvc-version.conf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\toolchain.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\default_pre.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\win32\default_pre.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\resolve_config.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\exclusive_builds_post.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\default_post.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\build_pass.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\win32\console.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\qml_debug.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\precompile_header.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\warn_on.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\permissions.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\qt.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\resources_functions.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\resources.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\moc.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\win32\opengl.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\uic.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\qmake_use.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\file_copies.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\testcase_targets.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\exceptions.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\yacc.prf ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\lex.prf ..\..\AIVersion.pro ..\..\..\..\QT\6.9.1\msvc2022_64\lib\Qt6MultimediaWidgets.prl ..\..\..\..\QT\6.9.1\msvc2022_64\lib\Qt6Widgets.prl ..\..\..\..\QT\6.9.1\msvc2022_64\lib\Qt6Multimedia.prl ..\..\..\..\QT\6.9.1\msvc2022_64\lib\Qt6Gui.prl ..\..\..\..\QT\6.9.1\msvc2022_64\lib\Qt6Network.prl ..\..\..\..\QT\6.9.1\msvc2022_64\lib\Qt6Core.prl    ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\data\dummy.cpp ..\..\mainwindow.h ..\..\OpenCVVisionEngine.h ..\..\VideoFrameCapture.h ..\..\GeminiAPIManager.h ..\..\DetectionOverlay.h  ..\..\main.cpp ..\..\mainwindow.cpp ..\..\OpenCVVisionEngine.cpp ..\..\VideoFrameCapture.cpp ..\..\GeminiAPIManager.cpp ..\..\DetectionOverlay.cpp     

clean: compiler_clean 
	-$(DEL_FILE) ..\release\obj\main.obj ..\release\obj\mainwindow.obj ..\release\obj\OpenCVVisionEngine.obj ..\release\obj\VideoFrameCapture.obj ..\release\obj\GeminiAPIManager.obj ..\release\obj\DetectionOverlay.obj ..\release\obj\moc_mainwindow.obj ..\release\obj\moc_OpenCVVisionEngine.obj ..\release\obj\moc_VideoFrameCapture.obj ..\release\obj\moc_GeminiAPIManager.obj ..\release\obj\moc_DetectionOverlay.obj
	-$(DEL_FILE) "D:\QT  pros\AIVersion\build\release\obj\version.res"

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: ..\release\moc\moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) ..\release\moc\moc_predefs.h
..\release\moc\moc_predefs.h: ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\data\dummy.cpp
	cl -BxD:\QT\6.9.1\msvc2022_64\bin\qmake.exe -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -permissive- -Zc:__cplusplus -Zc:externConstexpr -utf-8 -O2 -MD -std:c++17 -utf-8 -W3 -w34100 -w34189 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E ..\..\..\..\QT\6.9.1\msvc2022_64\mkspecs\features\data\dummy.cpp 2>NUL >..\release\moc\moc_predefs.h

compiler_moc_header_make_all: ..\release\moc\moc_mainwindow.cpp ..\release\moc\moc_OpenCVVisionEngine.cpp ..\release\moc\moc_VideoFrameCapture.cpp ..\release\moc\moc_GeminiAPIManager.cpp ..\release\moc\moc_DetectionOverlay.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) ..\release\moc\moc_mainwindow.cpp ..\release\moc\moc_OpenCVVisionEngine.cpp ..\release\moc\moc_VideoFrameCapture.cpp ..\release\moc\moc_GeminiAPIManager.cpp ..\release\moc\moc_DetectionOverlay.cpp
..\release\moc\moc_mainwindow.cpp: ..\..\mainwindow.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMainWindow \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmainwindow.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QVBoxLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qboxlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlayoutitem.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qgridlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QHBoxLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QLabel \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlabel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpicture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextdocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QPushButton \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qpushbutton.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractbutton.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QTextEdit \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtextedit.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractscrollarea.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMenuBar \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmenubar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmenu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QStatusBar \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstatusbar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QInputDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qinputdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlineedit.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMessageBox \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmessagebox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QCamera \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qcamera.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qcameradevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtvideo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimedia-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframeformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaCaptureSession \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediacapturesession.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\QVideoWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qvideowidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qtmultimediawidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qtmultimediawidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QCameraDevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaDevices \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediadevices.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QGroupBox \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qgroupbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFileDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qfiledialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdir.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdirlisting.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfiledevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfile.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfileinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimezone.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QSlider \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qslider.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractslider.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFormLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qformlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QListWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlistwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlistview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qitemselectionmodel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemdelegate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyleoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractspinbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvalidator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyle.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabbar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qrubberband.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFileInfo \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDateTime \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QApplication \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QImageCapture \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qimagecapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaRecorder \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediarecorder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediametadata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStandardPaths \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstandardpaths.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDir \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QUrl \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QProgressDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qprogressdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPixmap \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QScrollArea \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qscrollarea.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFrame \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFontMetrics \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMap \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QClipboard \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qclipboard.h \
		..\..\DetectionOverlay.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFont \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\OpenCVVisionEngine.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStringList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QColor \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv_modules.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvdef.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\interface.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_dispatch.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_helper.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\msa_macros.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\fast_math.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd_wrapper.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\neon_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\vsx_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\check.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\traits.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\saturate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\bufferpool.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\persistence.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\operations.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utility.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\instrumentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\tls.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\optim.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\ovx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\calib3d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\features2d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\miniflann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\defines.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\config.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\affine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.defines.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logtag.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\async.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dict.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\layer.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\utils\inference_engine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\flann_base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\general.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\matrix.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\params.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\any.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\saving.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\nn_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\result_set.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\all_indices.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dynamic_bitset.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dist.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\heap.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\allocator.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\random.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_single_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kmeans_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\logger.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\composite_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\linear_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\hierarchical_clustering_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_table.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\autotuned_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\ground_truth.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\index_testing.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\timer.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\sampling.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\highgui.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgcodecs.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\videoio.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc\segmentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml\ml.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_dictionary.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_board.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\graphical_code_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\detection_based_tracker.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\face.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\charuco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\barcode.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\photo.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda_types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\matchers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\motion_estimators.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\camera.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\exposure_compensate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\seam_finders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\blenders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\tracking.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\background_segm.hpp \
		..\..\VideoFrameCapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoSink \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideosink.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoFrame \
		..\..\GeminiAPIManager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFlags \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMetaType \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QIODevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\quuid.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonArray \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSettings \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsettings.h \
		..\release\moc\moc_predefs.h \
		..\..\..\..\QT\6.9.1\msvc2022_64\bin\moc.exe
	D:\QT\6.9.1\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include "D:/QT  pros/AIVersion/build/release/moc/moc_predefs.h" -ID:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc -I"D:/QT  pros/AIVersion" -ID:/opencv/build/include -I"D:/QT  pros/AIVersion" -ID:/QT/6.9.1/msvc2022_64/include -ID:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtMultimedia -ID:/QT/6.9.1/msvc2022_64/include/QtGui -ID:/QT/6.9.1/msvc2022_64/include/QtNetwork -ID:/QT/6.9.1/msvc2022_64/include/QtCore -I. -ID:\VS2\VC\Tools\MSVC\14.41.34120\include -ID:\VS2\VC\Tools\MSVC\14.41.34120\ATLMFC\include -ID:\VS2\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\mainwindow.h -o ..\release\moc\moc_mainwindow.cpp

..\release\moc\moc_OpenCVVisionEngine.cpp: ..\..\OpenCVVisionEngine.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStringList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QColor \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv_modules.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvdef.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\interface.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_dispatch.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_helper.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\msa_macros.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\fast_math.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd_wrapper.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\neon_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\vsx_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\check.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\traits.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\saturate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\bufferpool.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\persistence.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\operations.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utility.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\instrumentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\tls.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\optim.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\ovx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\calib3d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\features2d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\miniflann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\defines.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\config.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\affine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.defines.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logtag.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\async.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dict.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\layer.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\utils\inference_engine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\flann_base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\general.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\matrix.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\params.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\any.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\saving.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\nn_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\result_set.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\all_indices.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dynamic_bitset.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dist.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\heap.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\allocator.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\random.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_single_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kmeans_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\logger.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\composite_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\linear_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\hierarchical_clustering_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_table.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\autotuned_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\ground_truth.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\index_testing.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\timer.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\sampling.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\highgui.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgcodecs.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\videoio.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc\segmentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml\ml.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_dictionary.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_board.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\graphical_code_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\detection_based_tracker.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\face.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\charuco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\barcode.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\photo.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda_types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\matchers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\motion_estimators.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\camera.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\exposure_compensate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\seam_finders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\blenders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\tracking.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\background_segm.hpp \
		..\release\moc\moc_predefs.h \
		..\..\..\..\QT\6.9.1\msvc2022_64\bin\moc.exe
	D:\QT\6.9.1\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include "D:/QT  pros/AIVersion/build/release/moc/moc_predefs.h" -ID:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc -I"D:/QT  pros/AIVersion" -ID:/opencv/build/include -I"D:/QT  pros/AIVersion" -ID:/QT/6.9.1/msvc2022_64/include -ID:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtMultimedia -ID:/QT/6.9.1/msvc2022_64/include/QtGui -ID:/QT/6.9.1/msvc2022_64/include/QtNetwork -ID:/QT/6.9.1/msvc2022_64/include/QtCore -I. -ID:\VS2\VC\Tools\MSVC\14.41.34120\include -ID:\VS2\VC\Tools\MSVC\14.41.34120\ATLMFC\include -ID:\VS2\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\OpenCVVisionEngine.h -o ..\release\moc\moc_OpenCVVisionEngine.cpp

..\release\moc\moc_VideoFrameCapture.cpp: ..\..\VideoFrameCapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoSink \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideosink.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimedia-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoFrame \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtvideo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframeformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\release\moc\moc_predefs.h \
		..\..\..\..\QT\6.9.1\msvc2022_64\bin\moc.exe
	D:\QT\6.9.1\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include "D:/QT  pros/AIVersion/build/release/moc/moc_predefs.h" -ID:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc -I"D:/QT  pros/AIVersion" -ID:/opencv/build/include -I"D:/QT  pros/AIVersion" -ID:/QT/6.9.1/msvc2022_64/include -ID:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtMultimedia -ID:/QT/6.9.1/msvc2022_64/include/QtGui -ID:/QT/6.9.1/msvc2022_64/include/QtNetwork -ID:/QT/6.9.1/msvc2022_64/include/QtCore -I. -ID:\VS2\VC\Tools\MSVC\14.41.34120\include -ID:\VS2\VC\Tools\MSVC\14.41.34120\ATLMFC\include -ID:\VS2\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\VideoFrameCapture.h -o ..\release\moc\moc_VideoFrameCapture.cpp

..\release\moc\moc_GeminiAPIManager.cpp: ..\..\GeminiAPIManager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QUrl \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFlags \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMetaType \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QIODevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\quuid.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonArray \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSettings \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsettings.h \
		..\release\moc\moc_predefs.h \
		..\..\..\..\QT\6.9.1\msvc2022_64\bin\moc.exe
	D:\QT\6.9.1\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include "D:/QT  pros/AIVersion/build/release/moc/moc_predefs.h" -ID:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc -I"D:/QT  pros/AIVersion" -ID:/opencv/build/include -I"D:/QT  pros/AIVersion" -ID:/QT/6.9.1/msvc2022_64/include -ID:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtMultimedia -ID:/QT/6.9.1/msvc2022_64/include/QtGui -ID:/QT/6.9.1/msvc2022_64/include/QtNetwork -ID:/QT/6.9.1/msvc2022_64/include/QtCore -I. -ID:\VS2\VC\Tools\MSVC\14.41.34120\include -ID:\VS2\VC\Tools\MSVC\14.41.34120\ATLMFC\include -ID:\VS2\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\GeminiAPIManager.h -o ..\release\moc\moc_GeminiAPIManager.cpp

..\release\moc\moc_DetectionOverlay.cpp: ..\..\DetectionOverlay.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFont \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFontMetrics \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\release\moc\moc_predefs.h \
		..\..\..\..\QT\6.9.1\msvc2022_64\bin\moc.exe
	D:\QT\6.9.1\msvc2022_64\bin\moc.exe $(DEFINES) --compiler-flavor=msvc --include "D:/QT  pros/AIVersion/build/release/moc/moc_predefs.h" -ID:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc -I"D:/QT  pros/AIVersion" -ID:/opencv/build/include -I"D:/QT  pros/AIVersion" -ID:/QT/6.9.1/msvc2022_64/include -ID:/QT/6.9.1/msvc2022_64/include/QtMultimediaWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtWidgets -ID:/QT/6.9.1/msvc2022_64/include/QtMultimedia -ID:/QT/6.9.1/msvc2022_64/include/QtGui -ID:/QT/6.9.1/msvc2022_64/include/QtNetwork -ID:/QT/6.9.1/msvc2022_64/include/QtCore -I. -ID:\VS2\VC\Tools\MSVC\14.41.34120\include -ID:\VS2\VC\Tools\MSVC\14.41.34120\ATLMFC\include -ID:\VS2\VC\Auxiliary\VS\include -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.22621.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" ..\..\DetectionOverlay.h -o ..\release\moc\moc_DetectionOverlay.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

..\release\obj\main.obj: ..\..\main.cpp ..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QApplication \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\mainWindow.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMainWindow \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmainwindow.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QVBoxLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qboxlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlayoutitem.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qgridlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QHBoxLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QLabel \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlabel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpicture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextdocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QPushButton \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qpushbutton.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractbutton.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QTextEdit \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtextedit.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractscrollarea.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMenuBar \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmenubar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmenu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QStatusBar \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstatusbar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QInputDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qinputdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlineedit.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMessageBox \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmessagebox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QCamera \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qcamera.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qcameradevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtvideo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimedia-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframeformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaCaptureSession \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediacapturesession.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\QVideoWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qvideowidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qtmultimediawidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qtmultimediawidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QCameraDevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaDevices \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediadevices.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QGroupBox \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qgroupbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFileDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qfiledialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdir.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdirlisting.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfiledevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfile.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfileinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimezone.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QSlider \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qslider.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractslider.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFormLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qformlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QListWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlistwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlistview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qitemselectionmodel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemdelegate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyleoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractspinbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvalidator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyle.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabbar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qrubberband.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFileInfo \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDateTime \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QImageCapture \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qimagecapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaRecorder \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediarecorder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediametadata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStandardPaths \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstandardpaths.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDir \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QUrl \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QProgressDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qprogressdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPixmap \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QScrollArea \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qscrollarea.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFrame \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFontMetrics \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMap \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QClipboard \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qclipboard.h \
		..\..\DetectionOverlay.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFont \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\OpenCVVisionEngine.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStringList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QColor \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv_modules.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvdef.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\interface.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_dispatch.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_helper.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\msa_macros.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\fast_math.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd_wrapper.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\neon_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\vsx_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\check.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\traits.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\saturate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\bufferpool.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\persistence.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\operations.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utility.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\instrumentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\tls.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\optim.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\ovx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\calib3d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\features2d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\miniflann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\defines.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\config.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\affine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.defines.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logtag.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\async.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dict.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\layer.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\utils\inference_engine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\flann_base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\general.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\matrix.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\params.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\any.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\saving.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\nn_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\result_set.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\all_indices.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dynamic_bitset.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dist.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\heap.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\allocator.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\random.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_single_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kmeans_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\logger.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\composite_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\linear_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\hierarchical_clustering_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_table.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\autotuned_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\ground_truth.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\index_testing.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\timer.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\sampling.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\highgui.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgcodecs.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\videoio.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc\segmentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml\ml.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_dictionary.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_board.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\graphical_code_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\detection_based_tracker.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\face.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\charuco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\barcode.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\photo.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda_types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\matchers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\motion_estimators.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\camera.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\exposure_compensate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\seam_finders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\blenders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\tracking.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\background_segm.hpp \
		..\..\VideoFrameCapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoSink \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideosink.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoFrame \
		..\..\GeminiAPIManager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFlags \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMetaType \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QIODevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\quuid.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonArray \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSettings \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsettings.h

..\release\obj\mainwindow.obj: ..\..\mainwindow.cpp ..\..\MainWindow.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMainWindow \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmainwindow.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QVBoxLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qboxlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlayoutitem.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qgridlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QHBoxLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QLabel \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlabel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpicture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextdocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QPushButton \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qpushbutton.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractbutton.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QTextEdit \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtextedit.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractscrollarea.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMenuBar \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmenubar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmenu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QStatusBar \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstatusbar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QInputDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qinputdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlineedit.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QMessageBox \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qmessagebox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qdialogbuttonbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QCamera \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qcamera.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qcameradevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtvideo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimedia-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframeformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaCaptureSession \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediacapturesession.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\QVideoWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qvideowidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qtmultimediawidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimediaWidgets\qtmultimediawidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QCameraDevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaDevices \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediadevices.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QGroupBox \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qgroupbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFileDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qfiledialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdir.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdirlisting.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfiledevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfile.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfileinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimezone.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QSlider \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qslider.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractslider.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFormLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qformlayout.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QLayout \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QListWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlistwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qlistview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstractitemmodel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qitemselectionmodel.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractitemdelegate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyleoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qabstractspinbox.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvalidator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qstyle.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtabbar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qrubberband.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFileInfo \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDateTime \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QApplication \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QImageCapture \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qimagecapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QMediaRecorder \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediarecorder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qmediametadata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStandardPaths \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstandardpaths.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDir \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QUrl \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QProgressDialog \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qprogressdialog.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPixmap \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QScrollArea \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qscrollarea.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QFrame \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFontMetrics \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMap \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QClipboard \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qclipboard.h \
		..\..\DetectionOverlay.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFont \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\OpenCVVisionEngine.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStringList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QColor \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\opencv_modules.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvdef.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\interface.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_dispatch.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cv_cpu_helper.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\hal\msa_macros.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\fast_math.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd_wrapper.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\neon_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\vsx_utils.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\check.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\traits.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\saturate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\matx.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\bufferpool.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\mat.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\persistence.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\operations.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cvstd.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utility.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\instrumentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\tls.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\optim.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\ovx.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\calib3d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\features2d.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\miniflann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\defines.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\config.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\affine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logger.defines.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\utils\logtag.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\async.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\version.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dict.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\layer.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\dnn.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\dnn\utils\inference_engine.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\flann_base.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\general.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\matrix.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\params.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\any.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\saving.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\nn_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\result_set.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\all_indices.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dynamic_bitset.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\dist.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\heap.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\allocator.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\random.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kdtree_single_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\kmeans_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\logger.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\composite_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\linear_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\hierarchical_clustering_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\lsh_table.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\autotuned_index.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\ground_truth.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\index_testing.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\timer.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\flann\sampling.h \
		..\..\..\..\..\..\opencv\build\include\opencv2\highgui.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgcodecs.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\videoio.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\imgproc\segmentation.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\ml\ml.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_dictionary.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\aruco_board.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\graphical_code_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\detection_based_tracker.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\face.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\charuco_detector.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\objdetect\barcode.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\photo.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda_types.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\core\cuda.inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\warpers_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\matchers.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\motion_estimators.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\util_inl.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\camera.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\exposure_compensate.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\seam_finders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\stitching\detail\blenders.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\tracking.hpp \
		..\..\..\..\..\..\opencv\build\include\opencv2\video\background_segm.hpp \
		..\..\VideoFrameCapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoSink \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideosink.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoFrame \
		..\..\GeminiAPIManager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFlags \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMetaType \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QIODevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\quuid.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonArray \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSettings \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsettings.h

..\release\obj\OpenCVVisionEngine.obj: ..\..\OpenCVVisionEngine.cpp 

..\release\obj\VideoFrameCapture.obj: ..\..\VideoFrameCapture.cpp ..\..\VideoFrameCapture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoSink \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideosink.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimedia-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtmultimediaexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\QVideoFrame \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframe.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qtvideo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtMultimedia\qvideoframeformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDebug \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h

..\release\obj\GeminiAPIManager.obj: ..\..\GeminiAPIManager.cpp ..\..\GeminiAPIManager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkAccessManager \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkaccessmanager.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetwork-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtnetworkexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkrequest.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhttpheaders.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSharedDataPointer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QUrl \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QVariant \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q26numeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslConfiguration \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslconfiguration.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qtcpsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qabstractsocket.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhostaddress.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslcertificate.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcryptographichash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatetime.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcalendar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qssl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QFlags \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QSslPreSharedKeyAuthenticator \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qsslpresharedkeyauthenticator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QMetaType \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkReply \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qnetworkreply.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QIODevice \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QNetworkRequest \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborvalue.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcborcommon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qregularexpression.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\quuid.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsondocument.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonparseerror.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonDocument \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QJsonArray \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qjsonarray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QImage \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSettings \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsettings.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QBuffer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbuffer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QHttpMultiPart \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\qhttpmultipart.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QByteArray \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtNetwork\QHttpPart \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QUrlQuery \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurlquery.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDebug \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QStandardPaths \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstandardpaths.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QDir \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdir.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdirlisting.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfiledevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfile.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfileinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimezone.h

..\release\obj\DetectionOverlay.obj: ..\..\DetectionOverlay.cpp ..\..\DetectionOverlay.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QWidget \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qwidget.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreglobal.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversionchecks.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfiginclude.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconfig.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcore-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtconfigmacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationdefinitions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompilerdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qprocessordetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsystemdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtcoreexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtdeprecationmarkers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtclasshelpermacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtpreprocessorsupport.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qassert.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtnoop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypes.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtversion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtypeinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsysinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlogging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qflags.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasicatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qatomic_cxx11.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qgenericatomic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qyieldcpu.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qconstructormacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdarwinhelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexceptionhandling.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qforeach.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttypetraits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qglobalstatic.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmalloc.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qminmax.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnumeric.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qoverload.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qswap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtenvironmentvariables.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtresource.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qttranslation.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qversiontagging.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtgui-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtguiexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgets-config.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qtwidgetsexports.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnamespace.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcompare.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstdlibdetection.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcomparehelpers.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20type_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtmetamacros.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobjectdefs_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfunctionaltools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qwindowdefs_win.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstring.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qchar.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrefcount.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpair.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydatapointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qarraydataops.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainertools_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qxptype_traits.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20functional.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q17memory.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearrayview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringfwd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringliteral.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlatin1stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qanystringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qutf8stringview.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringtokenizer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringbuilder.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringconverter_base.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhashfunctions.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbytearraylist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringlist.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qalgorithms.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qstringmatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbasictimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qabstracteventdispatcher.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qeventloop.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdeadlinetimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qelapsedtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetatype.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdatastream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevicebase.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfloat16.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmath.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtformat_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiterable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmetacontainer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontainerinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtaggedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qscopeguard.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qobject_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qbindingstorage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmargins.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q23utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20utility.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qaction.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qkeysequence.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qicon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsize.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpaintdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrect.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcolor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgb.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qrgba64.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qimage.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpixelformat.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtransform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpolygon.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qregion.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qspan.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\q20iterator.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qline.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvariant.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qdebug.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtextstream.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcontiguouscache.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qsharedpointer_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qshareddata_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qset.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qhash.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qvarlengtharray.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpalette.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbrush.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfont.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qendian.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontmetrics.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontinfo.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qfontvariableaxis.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qsizepolicy.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qcursor.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qbitmap.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qevent.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qiodevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qurl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qeventpoint.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvector2d.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qvectornd.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpointingdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputdevice.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QList \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QObject \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRect \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSize \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QSizeF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QTransform \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qnativeinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qscreen_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qcoreapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfutureinterface.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qmutex.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtsan_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qresultstore.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qfuture_impl.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthreadpool.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qthread.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qrunnable.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qexception.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qpromise.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qinputmethod.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qlocale.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qguiapplication_platform.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPainter \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpainter.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qtextoption.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\qpen.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QTimer \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\qtimer.h \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFont \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QFontMetrics \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QRectF \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtCore\QString \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtGui\QPaintEvent \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\QApplication \
		..\..\..\..\..\..\QT\6.9.1\msvc2022_64\include\QtWidgets\qapplication.h

..\release\obj\moc_mainwindow.obj: ..\release\moc\moc_mainwindow.cpp 

..\release\obj\moc_OpenCVVisionEngine.obj: ..\release\moc\moc_OpenCVVisionEngine.cpp 

..\release\obj\moc_VideoFrameCapture.obj: ..\release\moc\moc_VideoFrameCapture.cpp 

..\release\obj\moc_GeminiAPIManager.obj: ..\release\moc\moc_GeminiAPIManager.cpp 

..\release\obj\moc_DetectionOverlay.obj: ..\release\moc\moc_DetectionOverlay.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

