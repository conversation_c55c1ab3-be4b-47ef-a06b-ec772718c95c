#include "TensorFlowLiteEngine.h"
#include <QDebug>
#include <QElapsedTimer>
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QApplication>
#include <cmath>

#if defined(_MSC_VER)
#pragma execution_character_set("utf-8")
#endif

// TensorFlow Lite头文件
#ifdef TENSORFLOW_LITE_AVAILABLE
#include "tensorflow/lite/interpreter.h"
#include "tensorflow/lite/kernels/register.h"
#include "tensorflow/lite/model.h"
#include "tensorflow/lite/optional_debug_tools.h"
#include "tensorflow/lite/delegates/gpu/delegate.h"
#include "tensorflow/lite/delegates/nnapi/nnapi_delegate.h"
#endif

TensorFlowLiteEngine::TensorFlowLiteEngine(QObject* parent)
    : QObject(parent)
    , interpreter_(nullptr)
    , model_(nullptr)
    , modelType_(Unknown)
    , modelLoaded_(false)
    , accelerationType_(CPU)
    , confidenceThreshold_(0.5f)
    , nmsThreshold_(0.4f)
    , maxDetections_(10)
    , inputTensorIndex_(-1)
    , outputTensorIndex_(-1)
{
    qDebug() << "TensorFlow Lite Engine initialized";
    resetStats();
}

TensorFlowLiteEngine::~TensorFlowLiteEngine()
{
#ifdef TENSORFLOW_LITE_AVAILABLE
    if (interpreter_) {
        delete static_cast<tflite::Interpreter*>(interpreter_);
        interpreter_ = nullptr;
    }
    if (model_) {
        // FlatBufferModel使用智能指针，会自动释放
        model_ = nullptr;
    }
#endif
    qDebug() << "TensorFlow Lite Engine destroyed";
}

bool TensorFlowLiteEngine::loadModel(const QString& modelPath, ModelType type)
{
#ifndef TENSORFLOW_LITE_AVAILABLE
    emit inferenceError("TensorFlow Lite未编译支持。请重新编译项目并包含TensorFlow Lite库。");
    return false;
#else
    if (!QFileInfo::exists(modelPath)) {
        QString error = QString("模型文件不存在: %1").arg(modelPath);
        qDebug() << error;
        emit inferenceError(error);
        return false;
    }

    emit inferenceProgress("正在加载TensorFlow Lite模型...");

    try {
        // 加载模型
        auto model = tflite::FlatBufferModel::BuildFromFile(modelPath.toStdString().c_str());
        if (!model) {
            QString error = QString("无法加载模型文件: %1").arg(modelPath);
            qDebug() << error;
            emit inferenceError(error);
            return false;
        }

        // 创建解释器
        tflite::ops::builtin::BuiltinOpResolver resolver;
        tflite::InterpreterBuilder builder(*model, resolver);
        
        std::unique_ptr<tflite::Interpreter> interpreter;
        builder(&interpreter);
        
        if (!interpreter) {
            QString error = "无法创建TensorFlow Lite解释器";
            qDebug() << error;
            emit inferenceError(error);
            return false;
        }

        // 分配张量内存
        if (interpreter->AllocateTensors() != kTfLiteOk) {
            QString error = "无法分配张量内存";
            qDebug() << error;
            emit inferenceError(error);
            return false;
        }

        // 清理旧的资源
        if (interpreter_) {
            delete static_cast<tflite::Interpreter*>(interpreter_);
        }

        // 保存新的资源
        interpreter_ = interpreter.release();
        model_ = model.release();
        currentModelPath_ = modelPath;
        modelType_ = (type == Unknown) ? detectModelType(modelPath) : type;
        modelLoaded_ = true;

        // 获取输入输出信息
        auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);
        inputTensorIndex_ = tfliteInterpreter->inputs()[0];
        outputTensorIndex_ = tfliteInterpreter->outputs()[0];

        // 获取输入形状
        TfLiteTensor* inputTensor = tfliteInterpreter->tensor(inputTensorIndex_);
        inputShape_.clear();
        for (int i = 0; i < inputTensor->dims->size; ++i) {
            inputShape_.push_back(inputTensor->dims->data[i]);
        }

        // 设置加速
        setupAcceleration();

        // 验证模型
        if (!validateModel()) {
            QString error = "模型验证失败";
            qDebug() << error;
            emit inferenceError(error);
            return false;
        }

        logModelInfo();
        emit inferenceProgress("模型加载成功！");
        
        qDebug() << "TensorFlow Lite模型加载成功:" << modelPath;
        qDebug() << "模型类型:" << (modelType_ == ImageClassification ? "图像分类" : 
                                   modelType_ == ObjectDetection ? "目标检测" : "未知");
        
        return true;

    } catch (const std::exception& e) {
        QString error = QString("加载模型时发生异常: %1").arg(e.what());
        qDebug() << error;
        emit inferenceError(error);
        return false;
    }
#endif
}

bool TensorFlowLiteEngine::loadLabels(const QString& labelsPath)
{
    if (!QFileInfo::exists(labelsPath)) {
        QString error = QString("标签文件不存在: %1").arg(labelsPath);
        qDebug() << error;
        emit inferenceError(error);
        return false;
    }

    QFile file(labelsPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QString error = QString("无法打开标签文件: %1").arg(labelsPath);
        qDebug() << error;
        emit inferenceError(error);
        return false;
    }

    classNames_.clear();
    QTextStream in(&file);
    in.setEncoding(QStringConverter::Utf8);
    
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (!line.isEmpty()) {
            classNames_.append(line);
        }
    }

    qDebug() << "加载了" << classNames_.size() << "个类别标签";
    return true;
}

void TensorFlowLiteEngine::setAccelerationType(AccelerationType type)
{
    accelerationType_ = type;
    if (modelLoaded_) {
        setupAcceleration();
    }
}

TFLiteInferenceResult TensorFlowLiteEngine::classifyImage(const QImage& image)
{
    TFLiteInferenceResult result;
    
    if (!modelLoaded_) {
        result.errorMessage = "模型未加载";
        emit inferenceError(result.errorMessage);
        return result;
    }

    if (modelType_ != ImageClassification) {
        result.errorMessage = "当前模型不是图像分类模型";
        emit inferenceError(result.errorMessage);
        return result;
    }

    return runInference(image);
}

TFLiteInferenceResult TensorFlowLiteEngine::detectObjects(const QImage& image)
{
    TFLiteInferenceResult result;
    
    if (!modelLoaded_) {
        result.errorMessage = "模型未加载";
        emit inferenceError(result.errorMessage);
        return result;
    }

    if (modelType_ != ObjectDetection) {
        result.errorMessage = "当前模型不是目标检测模型";
        emit inferenceError(result.errorMessage);
        return result;
    }

    return runInference(image);
}

TFLiteInferenceResult TensorFlowLiteEngine::runInference(const QImage& image)
{
    TFLiteInferenceResult result;
    
#ifndef TENSORFLOW_LITE_AVAILABLE
    result.errorMessage = "TensorFlow Lite未编译支持";
    emit inferenceError(result.errorMessage);
    return result;
#else
    
    if (!modelLoaded_ || !interpreter_) {
        result.errorMessage = "模型未加载或解释器无效";
        emit inferenceError(result.errorMessage);
        return result;
    }

    QElapsedTimer timer;
    timer.start();

    try {
        auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);

        // 预处理图像
        QImage processedImage = preprocessImage(image);
        std::vector<float> inputData = imageToFloatArray(processedImage);

        // 设置输入数据
        TfLiteTensor* inputTensor = tfliteInterpreter->tensor(inputTensorIndex_);
        memcpy(inputTensor->data.f, inputData.data(), inputData.size() * sizeof(float));

        // 运行推理
        if (tfliteInterpreter->Invoke() != kTfLiteOk) {
            result.errorMessage = "推理执行失败";
            updateStats(timer.elapsed(), false);
            emit inferenceError(result.errorMessage);
            return result;
        }

        result.inferenceTime = timer.elapsed();

        // 处理输出
        if (modelType_ == ImageClassification) {
            result = processClassificationOutput();
        } else if (modelType_ == ObjectDetection) {
            result = processDetectionOutput();
        } else {
            result.errorMessage = "未知的模型类型";
            updateStats(timer.elapsed(), false);
            emit inferenceError(result.errorMessage);
            return result;
        }

        result.success = true;
        result.inferenceTime = timer.elapsed();
        result.classNames = classNames_;

        updateStats(timer.elapsed(), true);
        emit inferenceCompleted(result);

        return result;

    } catch (const std::exception& e) {
        result.errorMessage = QString("推理过程中发生异常: %1").arg(e.what());
        updateStats(timer.elapsed(), false);
        emit inferenceError(result.errorMessage);
        return result;
    }
#endif
}

void TensorFlowLiteEngine::resetStats()
{
    stats_ = InferenceStats();
}

QStringList TensorFlowLiteEngine::getAvailableModels()
{
    QStringList models;

    // 检查models目录
    QString modelsDir = QDir::currentPath() + "/models";
    QDir dir(modelsDir);

    if (dir.exists()) {
        QStringList filters;
        filters << "*.tflite" << "*.lite";

        QFileInfoList files = dir.entryInfoList(filters, QDir::Files);
        for (const QFileInfo& file : files) {
            models.append(file.absoluteFilePath());
        }
    }

    return models;
}

bool TensorFlowLiteEngine::initializeInterpreter()
{
#ifdef TENSORFLOW_LITE_AVAILABLE
    if (!interpreter_) {
        return false;
    }

    auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);

    // 设置线程数
    tfliteInterpreter->SetNumThreads(QThread::idealThreadCount());

    return true;
#else
    return false;
#endif
}

bool TensorFlowLiteEngine::setupAcceleration()
{
#ifdef TENSORFLOW_LITE_AVAILABLE
    if (!interpreter_) {
        return false;
    }

    auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);

    switch (accelerationType_) {
    case GPU: {
        qDebug() << "尝试启用GPU加速...";
        // 注意：GPU加速需要特定的TensorFlow Lite版本和硬件支持
        break;
    }
    case NNAPI: {
        qDebug() << "尝试启用NNAPI加速...";
        // 注意：NNAPI主要用于Android平台
        break;
    }
    case Auto: {
        // 自动选择最佳加速方式
        accelerationType_ = CPU;
        break;
    }
    case CPU:
    default:
        qDebug() << "使用CPU推理";
        break;
    }

    return initializeInterpreter();
#else
    return false;
#endif
}

QImage TensorFlowLiteEngine::preprocessImage(const QImage& image)
{
    if (inputShape_.size() < 4) {
        return image;
    }

    // 获取模型期望的输入尺寸
    int height = inputShape_[1];
    int width = inputShape_[2];

    // 缩放图像到模型输入尺寸
    QImage scaledImage = image.scaled(width, height, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);

    // 转换为RGB格式
    if (scaledImage.format() != QImage::Format_RGB888) {
        scaledImage = scaledImage.convertToFormat(QImage::Format_RGB888);
    }

    return scaledImage;
}

std::vector<float> TensorFlowLiteEngine::imageToFloatArray(const QImage& image)
{
    std::vector<float> result;

    if (image.isNull()) {
        return result;
    }

    int width = image.width();
    int height = image.height();
    int channels = 3; // RGB

    result.reserve(width * height * channels);

    // 将图像数据转换为浮点数组 (归一化到0-1)
    for (int y = 0; y < height; ++y) {
        for (int x = 0; x < width; ++x) {
            QRgb pixel = image.pixel(x, y);
            result.push_back(qRed(pixel) / 255.0f);
            result.push_back(qGreen(pixel) / 255.0f);
            result.push_back(qBlue(pixel) / 255.0f);
        }
    }

    return result;
}

TFLiteInferenceResult TensorFlowLiteEngine::processClassificationOutput()
{
    TFLiteInferenceResult result;

#ifdef TENSORFLOW_LITE_AVAILABLE
    auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);
    TfLiteTensor* outputTensor = tfliteInterpreter->tensor(outputTensorIndex_);

    float* output = outputTensor->data.f;
    int outputSize = outputTensor->bytes / sizeof(float);

    // 获取分类结果
    result.classifications.clear();
    for (int i = 0; i < outputSize; ++i) {
        result.classifications.push_back(output[i]);
    }

    // 找到置信度最高的类别
    int maxIndex = 0;
    float maxConfidence = output[0];
    for (int i = 1; i < outputSize; ++i) {
        if (output[i] > maxConfidence) {
            maxConfidence = output[i];
            maxIndex = i;
        }
    }

    // 创建检测结果
    if (maxConfidence >= confidenceThreshold_) {
        QString className = (maxIndex < classNames_.size()) ?
                           classNames_[maxIndex] : QString("Class_%1").arg(maxIndex);

        TFLiteDetection detection(className, maxConfidence, QRectF(0, 0, 1, 1), maxIndex);
        result.detections.push_back(detection);
    }
#endif

    return result;
}

TFLiteInferenceResult TensorFlowLiteEngine::processDetectionOutput()
{
    TFLiteInferenceResult result;

#ifdef TENSORFLOW_LITE_AVAILABLE
    auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);

    // 目标检测模型通常有多个输出：boxes, classes, scores, num_detections
    // 这里提供一个通用的处理框架，具体实现需要根据模型结构调整

    if (tfliteInterpreter->outputs().size() >= 4) {
        // 标准的目标检测模型输出格式
        TfLiteTensor* boxesTensor = tfliteInterpreter->tensor(tfliteInterpreter->outputs()[0]);
        TfLiteTensor* classesTensor = tfliteInterpreter->tensor(tfliteInterpreter->outputs()[1]);
        TfLiteTensor* scoresTensor = tfliteInterpreter->tensor(tfliteInterpreter->outputs()[2]);
        TfLiteTensor* numDetectionsTensor = tfliteInterpreter->tensor(tfliteInterpreter->outputs()[3]);

        float* boxes = boxesTensor->data.f;
        float* classes = classesTensor->data.f;
        float* scores = scoresTensor->data.f;
        int numDetections = static_cast<int>(numDetectionsTensor->data.f[0]);

        numDetections = std::min(numDetections, maxDetections_);

        for (int i = 0; i < numDetections; ++i) {
            float score = scores[i];
            if (score >= confidenceThreshold_) {
                int classId = static_cast<int>(classes[i]);
                QString className = (classId < classNames_.size()) ?
                                   classNames_[classId] : QString("Class_%1").arg(classId);

                // 边界框坐标 (通常是 [y1, x1, y2, x2] 格式)
                float y1 = boxes[i * 4 + 0];
                float x1 = boxes[i * 4 + 1];
                float y2 = boxes[i * 4 + 2];
                float x2 = boxes[i * 4 + 3];

                QRectF boundingBox(x1, y1, x2 - x1, y2 - y1);

                TFLiteDetection detection(className, score, boundingBox, classId);
                result.detections.push_back(detection);
            }
        }
    }
#endif

    return result;
}

void TensorFlowLiteEngine::updateStats(float inferenceTime, bool success)
{
    stats_.totalInferences++;

    if (success) {
        stats_.successfulInferences++;

        // 更新推理时间统计
        if (stats_.totalInferences == 1) {
            stats_.minInferenceTime = inferenceTime;
            stats_.maxInferenceTime = inferenceTime;
            stats_.averageInferenceTime = inferenceTime;
        } else {
            stats_.minInferenceTime = std::min(stats_.minInferenceTime, inferenceTime);
            stats_.maxInferenceTime = std::max(stats_.maxInferenceTime, inferenceTime);

            // 计算移动平均
            float alpha = 0.1f; // 平滑因子
            stats_.averageInferenceTime = alpha * inferenceTime + (1.0f - alpha) * stats_.averageInferenceTime;
        }
    } else {
        stats_.failedInferences++;
    }

    emit statsUpdated(stats_);
}

TensorFlowLiteEngine::ModelType TensorFlowLiteEngine::detectModelType(const QString& modelPath)
{
    // 根据文件名推测模型类型
    QString fileName = QFileInfo(modelPath).fileName().toLower();

    if (fileName.contains("classification") || fileName.contains("mobilenet") ||
        fileName.contains("resnet") || fileName.contains("efficientnet")) {
        return ImageClassification;
    } else if (fileName.contains("detection") || fileName.contains("yolo") ||
               fileName.contains("ssd") || fileName.contains("rcnn")) {
        return ObjectDetection;
    }

    // 默认假设为图像分类
    return ImageClassification;
}

bool TensorFlowLiteEngine::validateModel()
{
#ifdef TENSORFLOW_LITE_AVAILABLE
    if (!interpreter_) {
        return false;
    }

    auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);

    // 检查输入输出张量
    if (tfliteInterpreter->inputs().empty() || tfliteInterpreter->outputs().empty()) {
        qDebug() << "模型没有有效的输入或输出张量";
        return false;
    }

    // 检查输入形状
    TfLiteTensor* inputTensor = tfliteInterpreter->tensor(inputTensorIndex_);
    if (inputTensor->dims->size != 4) {
        qDebug() << "输入张量维度不正确，期望4维，实际" << inputTensor->dims->size << "维";
        return false;
    }

    return true;
#else
    return false;
#endif
}

void TensorFlowLiteEngine::logModelInfo()
{
#ifdef TENSORFLOW_LITE_AVAILABLE
    if (!interpreter_) {
        return;
    }

    auto* tfliteInterpreter = static_cast<tflite::Interpreter*>(interpreter_);

    qDebug() << "=== TensorFlow Lite模型信息 ===";
    qDebug() << "模型路径:" << currentModelPath_;
    qDebug() << "模型类型:" << (modelType_ == ImageClassification ? "图像分类" :
                               modelType_ == ObjectDetection ? "目标检测" : "未知");

    // 输入信息
    TfLiteTensor* inputTensor = tfliteInterpreter->tensor(inputTensorIndex_);
    qDebug() << "输入张量形状:";
    for (int i = 0; i < inputTensor->dims->size; ++i) {
        qDebug() << "  维度" << i << ":" << inputTensor->dims->data[i];
    }

    // 输出信息
    qDebug() << "输出张量数量:" << tfliteInterpreter->outputs().size();
    for (size_t i = 0; i < tfliteInterpreter->outputs().size(); ++i) {
        TfLiteTensor* outputTensor = tfliteInterpreter->tensor(tfliteInterpreter->outputs()[i]);
        qDebug() << "输出张量" << i << "形状:";
        for (int j = 0; j < outputTensor->dims->size; ++j) {
            qDebug() << "  维度" << j << ":" << outputTensor->dims->data[j];
        }
    }

    qDebug() << "类别数量:" << classNames_.size();
    qDebug() << "加速类型:" << (accelerationType_ == CPU ? "CPU" :
                              accelerationType_ == GPU ? "GPU" :
                              accelerationType_ == NNAPI ? "NNAPI" : "Auto");
    qDebug() << "==============================";
#endif
}
