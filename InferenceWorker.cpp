#include "InferenceWorker.h"
#include <QDebug>
#include <QDateTime>
#include <QMutexLocker>
#include <QCoreApplication>
#include <algorithm>

#if defined(_MSC_VER)
#pragma execution_character_set("utf-8")
#endif

InferenceWorker::InferenceWorker(QObject* parent)
    : QObject(parent)
    , tfliteEngine_(nullptr)
    , opencvEngine_(nullptr)
    , workerThread_(new QThread(this))
    , running_(false)
    , paused_(false)
    , busy_(false)
    , stopRequested_(false)
    , maxQueueSize_(100)
{
    // 将工作对象移动到工作线程
    this->moveToThread(workerThread_);
    
    // 连接线程信号
    connect(workerThread_, &QThread::started, this, &InferenceWorker::processNextTask);
    connect(workerThread_, &QThread::finished, workerThread_, &QThread::deleteLater);
    
    resetStats();
    qDebug() << "推理工作线程已创建";
}

InferenceWorker::~InferenceWorker()
{
    stop();
    if (workerThread_->isRunning()) {
        workerThread_->quit();
        workerThread_->wait(3000); // 等待最多3秒
    }
    qDebug() << "推理工作线程已销毁";
}

void InferenceWorker::setTensorFlowLiteEngine(TensorFlowLiteEngine* engine)
{
    QMutexLocker locker(&mutex_);
    tfliteEngine_ = engine;
    qDebug() << "TensorFlow Lite引擎已设置到推理工作线程";
}

void InferenceWorker::setOpenCVEngine(OpenCVVisionEngine* engine)
{
    QMutexLocker locker(&mutex_);
    opencvEngine_ = engine;
    qDebug() << "OpenCV引擎已设置到推理工作线程";
}

QString InferenceWorker::addTask(InferenceTaskType type, const QImage& image, 
                                int priority, const QVariantMap& parameters)
{
    QMutexLocker locker(&mutex_);
    
    // 检查队列大小
    if (taskQueue_.size() >= maxQueueSize_) {
        qDebug() << "任务队列已满，丢弃最旧的任务";
        taskQueue_.dequeue();
        stats_.failedTasks++;
    }
    
    // 创建新任务
    QString taskId = QUuid::createUuid().toString(QUuid::WithoutBraces);
    InferenceTask task(taskId, type, image, priority);
    task.parameters = parameters;
    
    // 添加到队列
    taskQueue_.enqueue(task);
    stats_.totalTasks++;
    
    // 按优先级排序
    sortTaskQueue();
    
    // 通知有新任务
    condition_.wakeOne();
    
    emit queueSizeChanged(taskQueue_.size());
    emitStatsUpdate();
    
    qDebug() << "添加推理任务:" << taskId << "类型:" << static_cast<int>(type) << "优先级:" << priority;
    return taskId;
}

bool InferenceWorker::cancelTask(const QString& taskId)
{
    QMutexLocker locker(&mutex_);
    
    for (int i = 0; i < taskQueue_.size(); ++i) {
        if (taskQueue_[i].taskId == taskId) {
            taskQueue_.removeAt(i);
            emit queueSizeChanged(taskQueue_.size());
            qDebug() << "取消任务:" << taskId;
            return true;
        }
    }
    
    qDebug() << "未找到要取消的任务:" << taskId;
    return false;
}

void InferenceWorker::clearTasks()
{
    QMutexLocker locker(&mutex_);
    int clearedCount = taskQueue_.size();
    taskQueue_.clear();
    
    emit queueSizeChanged(0);
    qDebug() << "清空任务队列，清除了" << clearedCount << "个任务";
}

int InferenceWorker::getQueueSize() const
{
    QMutexLocker locker(&mutex_);
    return taskQueue_.size();
}

InferenceWorker::PerformanceStats InferenceWorker::getPerformanceStats() const
{
    QMutexLocker locker(&mutex_);
    PerformanceStats stats = stats_;
    stats.queueSize = taskQueue_.size();
    stats.isRunning = running_;
    stats.isBusy = busy_;
    return stats;
}

void InferenceWorker::resetStats()
{
    QMutexLocker locker(&mutex_);
    stats_ = PerformanceStats();
}

void InferenceWorker::start()
{
    QMutexLocker locker(&mutex_);
    if (running_) {
        qDebug() << "推理工作线程已在运行";
        return;
    }
    
    running_ = true;
    paused_ = false;
    stopRequested_ = false;
    
    if (!workerThread_->isRunning()) {
        workerThread_->start();
    }
    
    condition_.wakeOne();
    emit workerStateChanged(running_, busy_);
    qDebug() << "推理工作线程已启动";
}

void InferenceWorker::stop()
{
    {
        QMutexLocker locker(&mutex_);
        if (!running_) {
            return;
        }
        
        running_ = false;
        stopRequested_ = true;
        condition_.wakeOne();
    }
    
    // 等待当前任务完成
    if (workerThread_->isRunning()) {
        workerThread_->quit();
        workerThread_->wait(5000);
    }
    
    emit workerStateChanged(false, false);
    qDebug() << "推理工作线程已停止";
}

void InferenceWorker::pause()
{
    QMutexLocker locker(&mutex_);
    paused_ = true;
    emit workerStateChanged(running_, busy_);
    qDebug() << "推理工作线程已暂停";
}

void InferenceWorker::resume()
{
    QMutexLocker locker(&mutex_);
    paused_ = false;
    condition_.wakeOne();
    emit workerStateChanged(running_, busy_);
    qDebug() << "推理工作线程已恢复";
}

void InferenceWorker::processNextTask()
{
    while (true) {
        InferenceTask task;
        bool hasTask = false;
        
        {
            QMutexLocker locker(&mutex_);
            
            // 检查是否需要停止
            if (stopRequested_ || !running_) {
                busy_ = false;
                emit workerStateChanged(running_, busy_);
                break;
            }
            
            // 检查是否暂停
            if (paused_) {
                busy_ = false;
                emit workerStateChanged(running_, busy_);
                condition_.wait(&mutex_);
                continue;
            }
            
            // 检查是否有任务
            if (taskQueue_.isEmpty()) {
                busy_ = false;
                emit workerStateChanged(running_, busy_);
                condition_.wait(&mutex_);
                continue;
            }
            
            // 获取下一个任务
            task = taskQueue_.dequeue();
            hasTask = true;
            busy_ = true;
            emit workerStateChanged(running_, busy_);
            emit queueSizeChanged(taskQueue_.size());
        }
        
        if (hasTask) {
            // 处理任务
            processTask(task);
        }
        
        // 让出CPU时间
        QCoreApplication::processEvents();
    }
}

void InferenceWorker::processTask(const InferenceTask& task)
{
    QElapsedTimer timer;
    timer.start();
    
    InferenceResult result;
    result.taskId = task.taskId;
    result.type = task.type;
    result.timestamp = QDateTime::currentMSecsSinceEpoch();
    
    try {
        // 根据任务类型选择处理方式
        if (task.type == InferenceTaskType::TensorFlowLite_Classification ||
            task.type == InferenceTaskType::TensorFlowLite_Detection) {
            result = processTensorFlowLiteTask(task);
        } else {
            result = processOpenCVTask(task);
        }
        
        result.processingTime = timer.elapsed();
        result.success = true;
        
        updateStats(result.processingTime, true);
        emit taskCompleted(result);
        
        qDebug() << "任务完成:" << task.taskId << "耗时:" << result.processingTime << "ms";
        
    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("处理任务时发生异常: %1").arg(e.what());
        result.processingTime = timer.elapsed();
        
        updateStats(result.processingTime, false);
        emit taskFailed(task.taskId, result.errorMessage);
        
        qDebug() << "任务失败:" << task.taskId << "错误:" << result.errorMessage;
    }
}

InferenceResult InferenceWorker::processTensorFlowLiteTask(const InferenceTask& task)
{
    InferenceResult result;
    result.taskId = task.taskId;
    result.type = task.type;

    if (!tfliteEngine_) {
        result.success = false;
        result.errorMessage = "TensorFlow Lite引擎未设置";
        return result;
    }

    // 执行TensorFlow Lite推理
    if (task.type == InferenceTaskType::TensorFlowLite_Classification) {
        result.tfliteResult = tfliteEngine_->classifyImage(task.inputImage);
    } else if (task.type == InferenceTaskType::TensorFlowLite_Detection) {
        result.tfliteResult = tfliteEngine_->detectObjects(task.inputImage);
    }

    result.success = result.tfliteResult.success;
    result.errorMessage = result.tfliteResult.errorMessage;

    return result;
}

InferenceResult InferenceWorker::processOpenCVTask(const InferenceTask& task)
{
    InferenceResult result;
    result.taskId = task.taskId;
    result.type = task.type;

    if (!opencvEngine_) {
        result.success = false;
        result.errorMessage = "OpenCV引擎未设置";
        return result;
    }

    // 执行OpenCV处理
    try {
        switch (task.type) {
        case InferenceTaskType::OpenCV_Detection: {
            // YOLO目标检测
            opencvEngine_->setVisionMode(OpenCVVisionEngine::YOLO_DETECTION);
            result.processedImage = opencvEngine_->processFrame(task.inputImage);
            result.opencvDetections = opencvEngine_->getLastDetections();
            break;
        }
        case InferenceTaskType::OpenCV_FaceDetection: {
            // 人脸检测
            opencvEngine_->setVisionMode(OpenCVVisionEngine::FACE_DETECTION);
            result.processedImage = opencvEngine_->processFrame(task.inputImage);
            result.opencvDetections = opencvEngine_->getLastDetections();
            break;
        }
        case InferenceTaskType::OpenCV_MotionDetection: {
            // 运动检测
            opencvEngine_->setVisionMode(OpenCVVisionEngine::MOTION_DETECTION);
            result.processedImage = opencvEngine_->processFrame(task.inputImage);
            result.opencvDetections = opencvEngine_->getLastDetections();
            break;
        }
        case InferenceTaskType::OpenCV_EdgeDetection: {
            // 边缘检测
            opencvEngine_->setVisionMode(OpenCVVisionEngine::EDGE_DETECTION);
            result.processedImage = opencvEngine_->processFrame(task.inputImage);
            break;
        }
        default:
            result.success = false;
            result.errorMessage = "不支持的OpenCV任务类型";
            return result;
        }

        result.success = true;

    } catch (const std::exception& e) {
        result.success = false;
        result.errorMessage = QString("OpenCV处理失败: %1").arg(e.what());
    }

    return result;
}

void InferenceWorker::updateStats(float processingTime, bool success)
{
    QMutexLocker locker(&mutex_);

    if (success) {
        stats_.completedTasks++;

        // 更新处理时间统计
        if (stats_.completedTasks == 1) {
            stats_.minProcessingTime = processingTime;
            stats_.maxProcessingTime = processingTime;
            stats_.averageProcessingTime = processingTime;
        } else {
            stats_.minProcessingTime = std::min(stats_.minProcessingTime, processingTime);
            stats_.maxProcessingTime = std::max(stats_.maxProcessingTime, processingTime);

            // 计算移动平均
            float alpha = 0.1f;
            stats_.averageProcessingTime = alpha * processingTime +
                                         (1.0f - alpha) * stats_.averageProcessingTime;
        }
    } else {
        stats_.failedTasks++;
    }
}

void InferenceWorker::emitStatsUpdate()
{
    PerformanceStats stats = getPerformanceStats();
    emit performanceStatsUpdated(stats);
}

bool InferenceWorker::taskPriorityCompare(const InferenceTask& a, const InferenceTask& b)
{
    // 优先级高的排在前面
    if (a.priority != b.priority) {
        return a.priority > b.priority;
    }

    // 优先级相同时，时间戳早的排在前面
    return a.timestamp < b.timestamp;
}

void InferenceWorker::sortTaskQueue()
{
    // 将队列转换为列表进行排序
    QList<InferenceTask> taskList;
    while (!taskQueue_.isEmpty()) {
        taskList.append(taskQueue_.dequeue());
    }

    // 按优先级排序
    std::sort(taskList.begin(), taskList.end(), taskPriorityCompare);

    // 重新加入队列
    for (const InferenceTask& task : taskList) {
        taskQueue_.enqueue(task);
    }
}
