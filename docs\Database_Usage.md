# 数据库存储系统使用指南

本文档介绍如何使用项目中的SQLite数据库存储系统，包括识别记录、模型配置和用户设置的管理。

## 1. 数据库概述

项目使用SQLite数据库存储以下类型的数据：
- **识别记录** - 存储每次AI推理的结果和相关信息
- **模型配置** - 存储TensorFlow Lite和OpenCV模型的配置信息
- **用户设置** - 存储应用程序的用户配置和偏好设置

## 2. 数据库结构

### 2.1 识别记录表 (detection_records)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| session_id | TEXT | 会话ID，用于关联同一次使用会话的记录 |
| timestamp | DATETIME | 识别时间戳 |
| image_hash | TEXT | 图像哈希值，用于去重 |
| image_path | TEXT | 图像文件路径 |
| model_name | TEXT | 使用的模型名称 |
| model_type | TEXT | 模型类型 (classification/detection) |
| engine_type | TEXT | 引擎类型 (tensorflow_lite/opencv) |
| detection_results | TEXT | 检测结果 (JSON格式) |
| processing_time | REAL | 处理耗时(毫秒) |
| confidence | REAL | 平均置信度 |
| detection_count | INTEGER | 检测到的对象数量 |
| notes | TEXT | 备注信息 |
| created_at | DATETIME | 记录创建时间 |

### 2.2 模型配置表 (model_configs)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| model_name | TEXT | 模型名称，唯一 |
| model_path | TEXT | 模型文件路径 |
| labels_path | TEXT | 标签文件路径 |
| model_type | TEXT | 模型类型 |
| engine_type | TEXT | 引擎类型 |
| parameters | TEXT | 模型参数 (JSON格式) |
| is_default | BOOLEAN | 是否为默认模型 |
| is_enabled | BOOLEAN | 是否启用 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 2.3 用户设置表 (user_settings)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| category | TEXT | 设置分类 |
| key | TEXT | 设置键 |
| value | TEXT | 设置值 |
| description | TEXT | 设置描述 |
| updated_at | DATETIME | 更新时间 |

## 3. 基本使用方法

### 3.1 初始化数据库

```cpp
#include "DatabaseManager.h"

// 创建数据库管理器
DatabaseManager* dbManager = new DatabaseManager(this);

// 初始化数据库（使用默认路径）
if (dbManager->initialize()) {
    qDebug() << "数据库初始化成功";
} else {
    qDebug() << "数据库初始化失败";
}

// 或者指定数据库路径
if (dbManager->initialize("./data/my_database.db")) {
    qDebug() << "数据库初始化成功";
}
```

### 3.2 添加识别记录

```cpp
// 创建识别记录
DetectionRecord record;
record.sessionId = QUuid::createUuid().toString();
record.timestamp = QDateTime::currentDateTime();
record.imageHash = "image_hash_value";
record.imagePath = "/path/to/image.jpg";
record.modelName = "MobileNet V1";
record.modelType = "classification";
record.engineType = "tensorflow_lite";
record.processingTime = 25.5f;
record.confidence = 0.85f;
record.detectionCount = 1;
record.notes = "测试识别";

// 构建检测结果JSON
QJsonObject results;
QJsonArray detections;
QJsonObject detection;
detection["class_name"] = "cat";
detection["confidence"] = 0.85;
detection["bounding_box"] = QJsonArray{0.1, 0.2, 0.3, 0.4};
detections.append(detection);
results["detections"] = detections;
record.detectionResults = results;

// 添加到数据库
int recordId = dbManager->addDetectionRecord(record);
if (recordId > 0) {
    qDebug() << "识别记录添加成功，ID:" << recordId;
}
```

### 3.3 查询识别记录

```cpp
// 获取最近的100条记录
QList<DetectionRecord> records = dbManager->getDetectionRecords(100);

// 根据条件查询
QVariantMap conditions;
conditions["model_type"] = "classification";
conditions["engine_type"] = "tensorflow_lite";
QList<DetectionRecord> filteredRecords = dbManager->queryDetectionRecords(conditions, 50);

// 根据时间范围查询
QDateTime startTime = QDateTime::currentDateTime().addDays(-7); // 最近7天
QDateTime endTime = QDateTime::currentDateTime();
QList<DetectionRecord> recentRecords = dbManager->getDetectionRecordsByTimeRange(startTime, endTime);

// 根据会话ID查询
QString sessionId = "session_uuid";
QList<DetectionRecord> sessionRecords = dbManager->getDetectionRecordsBySession(sessionId);
```

### 3.4 管理模型配置

```cpp
// 添加模型配置
ModelConfig config;
config.modelName = "MobileNet V1";
config.modelPath = "/models/mobilenet_v1.tflite";
config.labelsPath = "/models/imagenet_labels.txt";
config.modelType = "classification";
config.engineType = "tensorflow_lite";
config.isDefault = true;
config.isEnabled = true;

// 设置模型参数
QJsonObject params;
params["confidence_threshold"] = 0.5;
params["input_size"] = 224;
config.parameters = params;

int configId = dbManager->addModelConfig(config);

// 获取模型配置
ModelConfig retrievedConfig = dbManager->getModelConfigByName("MobileNet V1");

// 获取默认分类模型
ModelConfig defaultClassificationModel = dbManager->getDefaultModelConfig("classification");

// 设置默认模型
dbManager->setDefaultModel("MobileNet V1", "classification");
```

### 3.5 管理用户设置

```cpp
// 设置用户配置
dbManager->setSetting("ui", "theme", "dark", "界面主题");
dbManager->setSetting("camera", "resolution", "1920x1080", "摄像头分辨率");
dbManager->setSetting("inference", "max_threads", "4", "最大推理线程数");

// 获取用户配置
QString theme = dbManager->getSetting("ui", "theme", "light"); // 默认值为"light"
QString resolution = dbManager->getSetting("camera", "resolution", "640x480");

// 获取分类下的所有设置
QVariantMap uiSettings = dbManager->getSettings("ui");
for (auto it = uiSettings.begin(); it != uiSettings.end(); ++it) {
    qDebug() << "UI设置:" << it.key() << "=" << it.value().toString();
}
```

## 4. 高级功能

### 4.1 数据库统计

```cpp
// 获取识别记录统计信息
QVariantMap stats = dbManager->getDetectionStatistics();
qDebug() << "总记录数:" << stats["total_records"].toInt();
qDebug() << "今日记录数:" << stats["today_records"].toInt();
qDebug() << "平均处理时间:" << stats["average_processing_time"].toFloat() << "ms";
qDebug() << "平均置信度:" << stats["average_confidence"].toFloat();
qDebug() << "最常用模型:" << stats["most_used_model"].toString();
```

### 4.2 数据库维护

```cpp
// 备份数据库
QString backupPath = "./backup/database_backup.db";
if (dbManager->backupDatabase(backupPath)) {
    qDebug() << "数据库备份成功";
}

// 恢复数据库
if (dbManager->restoreDatabase(backupPath)) {
    qDebug() << "数据库恢复成功";
}

// 优化数据库
if (dbManager->optimizeDatabase()) {
    qDebug() << "数据库优化成功";
}

// 检查数据库完整性
if (dbManager->checkDatabaseIntegrity()) {
    qDebug() << "数据库完整性检查通过";
}

// 获取数据库信息
QVariantMap dbInfo = dbManager->getDatabaseInfo();
qDebug() << "数据库路径:" << dbInfo["database_path"].toString();
qDebug() << "文件大小:" << dbInfo["file_size"].toLongLong() << "字节";
qDebug() << "表数量:" << dbInfo["tables"].toStringList().size();
```

### 4.3 信号连接

```cpp
// 连接数据库信号
connect(dbManager, &DatabaseManager::connectionStateChanged,
        this, [](bool connected) {
    qDebug() << "数据库连接状态:" << (connected ? "已连接" : "已断开");
});

connect(dbManager, &DatabaseManager::databaseError,
        this, [](const QString& errorMessage) {
    qDebug() << "数据库错误:" << errorMessage;
});

connect(dbManager, &DatabaseManager::recordAdded,
        this, [](const QString& tableName, int recordId) {
    qDebug() << "记录已添加到" << tableName << "，ID:" << recordId;
});
```

## 5. 最佳实践

### 5.1 性能优化

1. **批量操作** - 对于大量数据操作，使用事务：
```cpp
// 开始事务
database_.transaction();

try {
    // 批量插入操作
    for (const auto& record : records) {
        dbManager->addDetectionRecord(record);
    }
    
    // 提交事务
    database_.commit();
} catch (...) {
    // 回滚事务
    database_.rollback();
}
```

2. **索引使用** - 数据库已自动创建常用字段的索引，查询时尽量使用这些字段。

3. **分页查询** - 对于大量数据，使用分页查询：
```cpp
int pageSize = 50;
int pageIndex = 0;
QList<DetectionRecord> records = dbManager->getDetectionRecords(pageSize, pageIndex * pageSize);
```

### 5.2 数据安全

1. **定期备份** - 建议定期备份数据库：
```cpp
// 每日备份
QString backupPath = QString("./backup/database_%1.db")
                    .arg(QDate::currentDate().toString("yyyyMMdd"));
dbManager->backupDatabase(backupPath);
```

2. **数据验证** - 在添加数据前进行验证：
```cpp
bool isValidRecord(const DetectionRecord& record) {
    return !record.sessionId.isEmpty() && 
           !record.modelName.isEmpty() && 
           record.timestamp.isValid();
}
```

### 5.3 错误处理

```cpp
// 检查数据库连接状态
if (!dbManager->isConnected()) {
    qDebug() << "数据库未连接，尝试重新初始化";
    dbManager->initialize();
}

// 处理数据库错误
connect(dbManager, &DatabaseManager::databaseError,
        this, [this](const QString& errorMessage) {
    // 记录错误日志
    qCritical() << "数据库错误:" << errorMessage;
    
    // 尝试恢复
    if (!dbManager->isConnected()) {
        dbManager->initialize();
    }
});
```

## 6. 故障排除

### 6.1 常见问题

1. **数据库文件权限问题**
   - 确保应用程序对数据库文件所在目录有读写权限
   - 检查文件路径是否正确

2. **SQLite驱动未找到**
   - 确保Qt SQL模块已正确链接
   - 检查SQLite驱动是否可用：`QSqlDatabase::drivers()`

3. **数据库锁定**
   - 确保同时只有一个应用程序实例访问数据库
   - 正确关闭数据库连接

### 6.2 调试技巧

```cpp
// 启用SQL查询日志
QLoggingCategory::setFilterRules("qt.sql.*.debug=true");

// 检查可用的数据库驱动
QStringList drivers = QSqlDatabase::drivers();
qDebug() << "可用的数据库驱动:" << drivers;

// 检查数据库连接状态
if (database_.isOpen()) {
    qDebug() << "数据库已打开:" << database_.databaseName();
} else {
    qDebug() << "数据库未打开，错误:" << database_.lastError().text();
}
```

## 7. 扩展功能

项目的数据库系统设计为可扩展的，您可以：

1. **添加新表** - 在`createTables()`方法中添加新的表创建逻辑
2. **扩展现有表** - 使用数据库迁移机制添加新字段
3. **自定义查询** - 使用`prepareQuery()`方法执行自定义SQL查询
4. **数据导出** - 实现数据导出到CSV、JSON等格式的功能

通过这个数据库存储系统，您可以有效地管理AI视觉平台的所有数据，为用户提供完整的数据追踪和分析功能。
