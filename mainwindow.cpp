#include "mainwindow.h"

MainWindow::MainWindow(QWidget* parent) : QMainWindow(parent) {
    setupUI();
    setupCamera();
    setupPerformanceMonitor();
    setupImageCapture();
    setupVideoRecording();
    setupOpenCVVision();
    setupFrameCapture();
    setupGeminiAPI();
}

void MainWindow::openCamera() {
    // 1. 获取可用摄像头列表
    const QList<QCameraDevice> cameras = QMediaDevices::videoInputs();   //: Qt多媒体模块提供的函数，用于扫描并返回系统上所有可用的视频输入设备（摄像头）

    if (cameras.isEmpty()) {
        QMessageBox::warning(this, "错误", "没有找到可用的摄像头设备");
        return;
    }

    // 2.选择摄像头
    QStringList cameraNames;
    for (const auto& camera : cameras) {
        cameraNames << camera.description();
    }

    //显示一个标准的下拉列表对话框，让用户选择
    bool ok;
    QString selectedCamera = QInputDialog::getItem(this,
                                                   "选择摄像头 - AI图像识别平台",
                                                   "选择摄像头:", cameraNames, 0, false, &ok);

    if (!ok) return;

    int selectedIndex = cameraNames.indexOf(selectedCamera);
    if (selectedIndex < 0) return;

    // 3.停止当前摄像头（如果有的话）
    if (camera) {
        camera->stop();
        delete camera;
    }

    // 4.根据用户的选择来创建新摄像头
    camera = new QCamera(cameras[selectedIndex], this);

    // 5. 将摄像头与会话(Session)和输出端连接
    captureSession->setCamera(camera);
    captureSession->setImageCapture(imageCapture);
    captureSession->setRecorder(mediaRecorder);

    //6. 连接摄像头状态变化的信号
    connect(camera, &QCamera::activeChanged, this, [this](bool active) {  //[this]表示这个Lambda函数可以访问MainWindow的成员（如openCameraButton）。
        if (active) {
            //摄像头启动，更新ui
            openCameraButton->setText("关闭摄像头");
            statusBar()->showMessage("摄像头已启动");
            updatePerformanceDisplay();

            // 摄像头启动后连接视频接收器并开始显示画面
            if (frameCapture && videoWidget) {
                QVideoSink* videoSink = videoWidget->videoSink();
                if (videoSink) {
                    frameCapture->setVideoSink(videoSink);
                    frameCapture->setEnabled(true); // 立即开始捕获帧用于显示
                    qDebug() << "Video sink connected and frame capture enabled for display";
                } else {
                    qDebug() << "Warning: Video sink not available after camera activation";
                }
            }
        } else {
            openCameraButton->setText("打开摄像头");
            statusBar()->showMessage("摄像头已停止");

            // 摄像头停止时禁用帧捕获并清空显示
            if (frameCapture) {
                frameCapture->setEnabled(false);
            }

            // 清空显示并显示提示信息
            if (detectionImageLabel) {
                detectionImageLabel->clear();
                detectionImageLabel->setText("请打开摄像头并开始识别");

                // 重置状态信息
                auto labels = findChildren<QLabel*>();
                for (auto* label : labels) {
                    if (label->text().contains("16:9")) {
                        label->setText("📺 16:9 宽屏显示 | 等待摄像头启动...");
                        break;
                    }
                }
            }
        }
    });

    connect(camera, &QCamera::errorOccurred, this, [this](QCamera::Error error, const QString &errorString) {
        QMessageBox::warning(this, "摄像头错误", errorString);
        statusBar()->showMessage("摄像头错误: " + errorString);
    });

    // 7.启动摄像头
    camera->start();

    // 8. 切换按钮的功能
    disconnect(openCameraButton, &QPushButton::clicked, this, &MainWindow::openCamera);
    connect(openCameraButton, &QPushButton::clicked, this, &MainWindow::closeCamera);
}

void MainWindow::closeCamera() {
    if (camera) {
        camera->stop();
    }

    openCameraButton->setText("打开摄像头");
    statusBar()->showMessage("摄像头已停止");

    disconnect(openCameraButton, &QPushButton::clicked, this, &MainWindow::closeCamera);
    connect(openCameraButton, &QPushButton::clicked, this, &MainWindow::openCamera);
}

void MainWindow::startInference() {
    if (!camera || !camera->isActive()) {
        QMessageBox::warning(this, "提示", "请先打开摄像头");
        return;
    }

    isInferenceRunning = !isInferenceRunning;
    if (isInferenceRunning) {
        startInferenceButton->setText("停止识别");
        startInferenceButton->setStyleSheet("QPushButton { background-color: #F44336; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #D32F2F; }");

        statusBar()->showMessage(QString("OpenCV识别已启动 - %1 (真实摄像头)").arg(openCVEngine->getCurrentModeDescription()));
        openCVEngine->setProcessingEnabled(true);
        processingTimer->start(100); // 每100ms处理一帧
    } else {
        startInferenceButton->setText("开始识别");
        startInferenceButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #1976D2; }");

        statusBar()->showMessage("识别已停止，继续显示预览");
        openCVEngine->setProcessingEnabled(false);
        processingTimer->stop();
        detectionOverlay->clearDetections();

        // 更新状态信息为预览模式
        auto labels = findChildren<QLabel*>();
        for (auto* label : labels) {
            if (label->text().contains("16:9")) {
                QString resInfo = label->text();
                resInfo.replace("🎯 检测:", "📹 实时预览");
                label->setText(resInfo);
                break;
            }
        }
    }
}

void MainWindow::captureImage() {
    if (!camera || !camera->isActive()) {
        QMessageBox::warning(this, "提示", "请先打开摄像头");
        return;
    }

    // 生成文件名
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString filename = QString("capture_%1.jpg").arg(timestamp);

    // 获取图片保存目录   QDir 是一个用来操作目录和文件路径的类。.filePath("AIVision") 这个方法会在 picturesPath 的基础上，追加一个子目录名
    QString picturesPath = QStandardPaths::writableLocation(QStandardPaths::PicturesLocation);
    QString savePath = QDir(picturesPath).filePath("AIVision");

    // 确保目录存在
    QDir().mkpath(savePath);

    QString fullPath = QDir(savePath).filePath(filename);

    // 捕获图像
    imageCapture->captureToFile(fullPath);

    statusBar()->showMessage(QString("图像已保存: %1").arg(filename), 3000);
}

void MainWindow::saveImageAs() {
    if (!camera || !camera->isActive()) {
        QMessageBox::warning(this, "提示", "请先打开摄像头");
        return;
    }

    QString filename = QFileDialog::getSaveFileName(this,
                                                    "保存图像",
                                                    QStandardPaths::writableLocation(QStandardPaths::PicturesLocation) + "/capture.jpg",
                                                    "图像文件 (*.jpg *.jpeg *.png *.bmp)");

    if (!filename.isEmpty()) {
        imageCapture->captureToFile(filename);
        statusBar()->showMessage("图像保存中...", 2000);
    }
}

void MainWindow::toggleRecording() {
    if (!camera || !camera->isActive()) {
        QMessageBox::warning(this, "提示", "请先打开摄像头");
        return;
    }
    //如果当前是停止状态 (StoppedState)，就调用 startRecording() 开始录制
    if (mediaRecorder->recorderState() == QMediaRecorder::StoppedState) {
        startRecording();
    } else {
        stopRecording();
    }
}

void MainWindow::startRecording() {
    // 1.生成文件名
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString filename = QString("recording_%1.mp4").arg(timestamp);

    // 2.获取视频保存目录
    QString videosPath = QStandardPaths::writableLocation(QStandardPaths::MoviesLocation);
    QString savePath = QDir(videosPath).filePath("AIVision");

    // 3.确保目录存在
    QDir().mkpath(savePath);

    QString fullPath = QDir(savePath).filePath(filename);

    // 4.设置输出位置并开始录制
    mediaRecorder->setOutputLocation(QUrl::fromLocalFile(fullPath));
    mediaRecorder->record();

    recordButton->setText("停止录制");
    recordButton->setStyleSheet("QPushButton { background-color: #F44336; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #D32F2F; }");
    statusBar()->showMessage(QString("开始录制: %1").arg(filename));
}

void MainWindow::stopRecording() {
    mediaRecorder->stop();
    recordButton->setText("开始录制");
    recordButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #45a049; }");
    statusBar()->showMessage("录制已停止", 2000);
}

void MainWindow::updatePerformanceDisplay() {
    QString perfText = QString(
                           "FPS: %1\n"
                           "延迟: %2ms\n"
                           "内存: %3MB\n"
                           "CPU: %4%\n"
                           "GPU: %5%"
                           ).arg(camera && camera->isActive() ? "30.0" : "0.0")
                           .arg(camera && camera->isActive() ? "33" : "0")
                           .arg("512")
                           .arg("25.0")
                           .arg("15.0");

    performanceText->setText(perfText);
}



void MainWindow::setupUI() {
    setWindowTitle("AI图像识别平台 - 2025");
    setMinimumSize(1200, 800);

    // 创建菜单栏
    auto* fileMenu = menuBar()->addMenu("文件");
    fileMenu->addAction("捕获图像", this, &MainWindow::captureImage);
    fileMenu->addAction("另存图像为...", this, &MainWindow::saveImageAs);
    fileMenu->addSeparator();    //添加一条分割线
    fileMenu->addAction("开始/停止录制", this, &MainWindow::toggleRecording);
    fileMenu->addSeparator();
    fileMenu->addAction("上传图片分析", this, &MainWindow::uploadAndAnalyzeImage);
    fileMenu->addAction("批量分析图片", this, &MainWindow::batchAnalyzeImages);
    fileMenu->addSeparator();
    fileMenu->addAction("退出", this, &QWidget::close);

    auto* visionMenu = menuBar()->addMenu("视觉模式");
    visionMenu->addAction("切换视觉模式", this, &MainWindow::switchVisionMode);
    visionMenu->addSeparator();
    visionMenu->addAction("配置Gemini API", this, &MainWindow::configureGeminiAPI);
    visionMenu->addAction("Gemini API信息", this, &MainWindow::showGeminiAPIInfo);

    auto* helpMenu = menuBar()->addMenu("帮助");
    helpMenu->addAction("关于", this, [this]() {
        QMessageBox::about(this, "关于", "AI图像识别平台 v1.0.0\n基于Qt6和TensorFlow Lite");
    });

    // 创建中央部件
    auto* central = new QWidget();
    setCentralWidget(central);  //将我们创建的 central 设置为窗口的中央部件

    auto* mainLayout = new QHBoxLayout(central);   //创建一个水平布局，以后加进来的东西在中央部件中水平布局

    // 左侧控制面板
    auto* leftPanel = new QWidget();
    leftPanel->setFixedWidth(250);
    leftPanel->setStyleSheet("background-color: #2b2b2b; color: white;");

    auto* leftLayout = new QVBoxLayout(leftPanel);   //为leftPanel创建一个垂直布局。

    // 摄像头控制
    //QGroupBox 是一个带标题和边框的容器，用于将相关的控件组织在一起
    auto* cameraGroup = new QGroupBox("摄像头控制");
    cameraGroup->setStyleSheet("QGroupBox { color: white; font-weight: bold; }");
    auto* cameraLayout = new QVBoxLayout(cameraGroup);

    openCameraButton = new QPushButton("打开摄像头");
    openCameraButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #45a049; }");
    connect(openCameraButton, &QPushButton::clicked, this, &MainWindow::openCamera);
    cameraLayout->addWidget(openCameraButton);  //: 把创建好的按钮添加到 cameraGroup 的垂直布局中

    leftLayout->addWidget(cameraGroup);

    // AI控制
    auto* aiGroup = new QGroupBox("AI控制");
    aiGroup->setStyleSheet("QGroupBox { color: white; font-weight: bold; }");
    auto* aiLayout = new QVBoxLayout(aiGroup);

    startInferenceButton = new QPushButton("开始识别");
    startInferenceButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #1976D2; }");
    connect(startInferenceButton, &QPushButton::clicked, this, &MainWindow::startInference);
    aiLayout->addWidget(startInferenceButton);

    leftLayout->addWidget(aiGroup);

    // 图像控制
    auto* imageGroup = new QGroupBox("图像控制");
    imageGroup->setStyleSheet("QGroupBox { color: white; font-weight: bold; }");
    auto* imageLayout = new QVBoxLayout(imageGroup);

    auto* captureButton = new QPushButton("捕获图像");
    captureButton->setStyleSheet("QPushButton { background-color: #FF9800; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #F57C00; }");
    connect(captureButton, &QPushButton::clicked, this, &MainWindow::captureImage);
    imageLayout->addWidget(captureButton);

    auto* saveAsButton = new QPushButton("另存为...");
    saveAsButton->setStyleSheet("QPushButton { background-color: #9C27B0; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #7B1FA2; }");
    connect(saveAsButton, &QPushButton::clicked, this, &MainWindow::saveImageAs);
    imageLayout->addWidget(saveAsButton);

    recordButton = new QPushButton("开始录制");
    recordButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #45a049; }");
    connect(recordButton, &QPushButton::clicked, this, &MainWindow::toggleRecording);
    imageLayout->addWidget(recordButton);

    // 添加分隔线
    auto* separator = new QFrame();
    separator->setFrameShape(QFrame::HLine);
    separator->setFrameShadow(QFrame::Sunken);
    separator->setStyleSheet("color: #555;");
    imageLayout->addWidget(separator);

    // 上传图片按钮
    uploadImageButton = new QPushButton("上传图片 (Gemini分析)");
    uploadImageButton->setStyleSheet("QPushButton { background-color: #E91E63; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #C2185B; }");
    connect(uploadImageButton, &QPushButton::clicked, this, &MainWindow::uploadAndAnalyzeImage);
    imageLayout->addWidget(uploadImageButton);

    // 批量分析按钮
    batchAnalyzeButton = new QPushButton("批量分析");
    batchAnalyzeButton->setStyleSheet("QPushButton { background-color: #607D8B; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #455A64; }");
    connect(batchAnalyzeButton, &QPushButton::clicked, this, &MainWindow::batchAnalyzeImages);
    imageLayout->addWidget(batchAnalyzeButton);

    leftLayout->addWidget(imageGroup);
    leftLayout->addStretch();

    mainLayout->addWidget(leftPanel);

    // 简单的视频显示区域
    auto* videoContainer = new QWidget();
    videoContainer->setStyleSheet(
        "QWidget {"
        "    background-color: #2b2b2b;"
        "    border-radius: 10px;"
        "    margin: 8px;"
        "}"
        );
    auto* videoLayout = new QVBoxLayout(videoContainer);
    videoLayout->setContentsMargins(25, 25, 25, 25);
    videoLayout->setAlignment(Qt::AlignCenter); // 居中对齐
    videoLayout->setSpacing(12); // 增加间距

    // 主显示区域 - 显示带检测结果的画面 (16:9比例，适中尺寸)
    detectionImageLabel = new QLabel();
    detectionImageLabel->setFixedSize(960, 540); // 固定为960x540，平衡大小和清晰度
    detectionImageLabel->setScaledContents(false); // 禁用自动缩放，手动控制
    detectionImageLabel->setAlignment(Qt::AlignCenter); // 内容居中对齐
    detectionImageLabel->setStyleSheet(
        "QLabel {"
        "    background-color: black;"
        "    border: 2px solid #555;"
        "    border-radius: 6px;"
        "}"
        );
    detectionImageLabel->setText("请打开摄像头并开始识别，或上传图片进行分析");

    videoLayout->addWidget(detectionImageLabel);

    // 创建图片显示区域（用于上传的图片）
    imageDisplayLabel = new QLabel();
    imageDisplayLabel->setAlignment(Qt::AlignCenter);
    imageDisplayLabel->setStyleSheet(
        "QLabel {"
        "    background-color: black;"
        "    border: 2px solid #555;"
        "    border-radius: 6px;"
        "}"
        );
    imageDisplayLabel->setVisible(false); // 初始隐藏

    // 创建滚动区域用于大图片显示
    imageScrollArea = new QScrollArea();
    imageScrollArea->setWidget(imageDisplayLabel);
    imageScrollArea->setWidgetResizable(true);
    imageScrollArea->setVisible(false); // 初始隐藏
    videoLayout->addWidget(imageScrollArea);

    // 添加分辨率信息标签
    auto* resolutionLabel = new QLabel("等待摄像头启动...");
    resolutionLabel->setStyleSheet(
        "QLabel {"
        "    color: #888;"
        "    font-size: 12px;"
        "    padding: 8px;"
        "    background-color: rgba(255, 255, 255, 0.1);"
        "    border-radius: 4px;"
        "    margin-top: 5px;"
        "}"
        );
    resolutionLabel->setAlignment(Qt::AlignCenter);
    videoLayout->addWidget(resolutionLabel);

    // 隐藏的原始视频控件（用于捕获帧）
    videoWidget = new QVideoWidget();
    videoWidget->setVisible(false); // 隐藏，只用于帧捕获

    // 创建检测结果叠加层（备用）
    detectionOverlay = new DetectionOverlay(detectionImageLabel);

    mainLayout->addWidget(videoContainer, 1);

    // 右侧性能监控
    auto* rightPanel = new QWidget();
    rightPanel->setFixedWidth(200);
    rightPanel->setStyleSheet("background-color: #2b2b2b; color: white;");

    auto* rightLayout = new QVBoxLayout(rightPanel);

    auto* perfGroup = new QGroupBox("性能监控");
    perfGroup->setStyleSheet("QGroupBox { color: white; font-weight: bold; }");
    auto* perfLayout = new QVBoxLayout(perfGroup);

    performanceText = new QTextEdit();
    performanceText->setStyleSheet("background-color: #1e1e1e; color: #00ff00; border: 1px solid #555; font-family: 'Courier New';");
    performanceText->setMaximumHeight(150);
    performanceText->setReadOnly(true);
    perfLayout->addWidget(performanceText);

    rightLayout->addWidget(perfGroup);
    rightLayout->addStretch();

    mainLayout->addWidget(rightPanel);

    // 状态栏
    statusBar()->showMessage("准备就绪");
}

void MainWindow::setupCamera() {
    camera = nullptr;
    captureSession = new QMediaCaptureSession(this);
    //虽然用户看不见videoWidget，但摄像头的数据流会源源不断地输送到这里。为后续的“帧捕获”操作（setupFrameCapture）提供了数据源。
    captureSession->setVideoOutput(videoWidget);
}

void MainWindow::setupImageCapture() {
    imageCapture = new QImageCapture(this);

    // 连接图像捕获信号
    connect(imageCapture, &QImageCapture::imageCaptured, this, [this](int id, const QImage& image) {
        Q_UNUSED(id)
        Q_UNUSED(image)
        statusBar()->showMessage("图像捕获成功", 2000);
    });

    connect(imageCapture, &QImageCapture::imageSaved, this, [this](int id, const QString& fileName) {
        Q_UNUSED(id)
        statusBar()->showMessage(QString("图像已保存: %1").arg(QFileInfo(fileName).fileName()), 3000);
    });

    connect(imageCapture, &QImageCapture::errorOccurred, this, [this](int id, QImageCapture::Error error, const QString& errorString) {
        Q_UNUSED(id)
        Q_UNUSED(error)
        QMessageBox::warning(this, "图像捕获错误", errorString);
        statusBar()->showMessage("图像捕获失败", 2000);
    });
}

void MainWindow::setupVideoRecording() {
    mediaRecorder = new QMediaRecorder(this);

    // 设置录制质量
    mediaRecorder->setQuality(QMediaRecorder::HighQuality);

    // 连接录制信号
    connect(mediaRecorder, &QMediaRecorder::recorderStateChanged, this, [this](QMediaRecorder::RecorderState state) {
        switch (state) {
        case QMediaRecorder::RecordingState:
            statusBar()->showMessage("正在录制...");
            break;
        case QMediaRecorder::PausedState:
            statusBar()->showMessage("录制已暂停");
            break;
        case QMediaRecorder::StoppedState:
            statusBar()->showMessage("录制已停止", 2000);
            break;
        }
    });

    connect(mediaRecorder, &QMediaRecorder::durationChanged, this, [this](qint64 duration) {
        int seconds = duration / 1000;
        int minutes = seconds / 60;
        seconds = seconds % 60;
        statusBar()->showMessage(QString("录制时间: %1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0')));
    });

    connect(mediaRecorder, &QMediaRecorder::errorOccurred, this, [this](QMediaRecorder::Error error, const QString& errorString) {
        Q_UNUSED(error)
        QMessageBox::warning(this, "录制错误", errorString);
        statusBar()->showMessage("录制失败", 2000);
    });
}

void MainWindow::setupPerformanceMonitor() {
    auto* perfTimer = new QTimer(this);
    connect(perfTimer, &QTimer::timeout, this, &MainWindow::updatePerformanceDisplay);
    perfTimer->start(1000); // 每秒更新一次

    updatePerformanceDisplay();
}



void MainWindow::setupOpenCVVision() {
    // 创建OpenCV视觉引擎
    openCVEngine = new OpenCVVisionEngine(this);

    // 连接OpenCV信号
    connect(openCVEngine, &OpenCVVisionEngine::detectionsReady,
            this, &MainWindow::onOpenCVDetectionReady);
    connect(openCVEngine, &OpenCVVisionEngine::statsUpdated,
            this, &MainWindow::onOpenCVStatsUpdated);
    connect(openCVEngine, &OpenCVVisionEngine::errorOccurred,
            this, [this](const QString& error) {
                QMessageBox::warning(this, "OpenCV错误", error);
                statusBar()->showMessage("OpenCV错误: " + error, 5000);
            });

    // 创建处理定时器
    processingTimer = new QTimer(this);
    connect(processingTimer, &QTimer::timeout, this, [this]() {
        if (camera && camera->isActive() && videoWidget) {
            // 优先使用真实摄像头帧
            if (frameCapture && frameCapture->hasFrame()) {
                QImage realFrame = frameCapture->getLatestFrame();
                if (!realFrame.isNull()) {
                    qDebug() << "获取真实的视频帧，大小是:" << realFrame.size();
                    openCVEngine->processFrame(realFrame);
                    return;
                }
            }

            // 回退到测试图像
            qDebug() << "Using test image (no real frame available)";
            QImage testImage = createTestImage();
            openCVEngine->processFrame(testImage);
        }
    });

    // 设置默认模式为人脸检测
    openCVEngine->setVisionMode(OpenCVVisionEngine::FaceDetection);
}

void MainWindow::switchVisionMode() {
    QStringList modes = OpenCVVisionEngine::getAvailableModes();
    bool ok;
    QString selectedMode = QInputDialog::getItem(this,
                                                 "选择视觉模式 - OpenCV计算机视觉",
                                                 "选择检测模式:",
                                                 modes, 0, false, &ok);

    if (ok) {
        int modeIndex = modes.indexOf(selectedMode);
        openCVEngine->setVisionMode(static_cast<OpenCVVisionEngine::VisionMode>(modeIndex));
        statusBar()->showMessage(QString("已切换到: %1").arg(openCVEngine->getCurrentModeDescription()), 3000);
    }
}

void MainWindow::onOpenCVDetectionReady(const std::vector<OpenCVDetection>& detections, const QImage& image) {
    Q_UNUSED(image)

    qDebug() << "检测到" << detections.size() << "个目标根据OpenCV";

    // 1.转换为通用格式
    std::vector<DetectionResult> results;
    std::vector<Detection> simpleDetections;

    for (const auto& detection : detections) {
        DetectionResult result;
        result.className = detection.description;
        result.confidence = detection.confidence;
        result.boundingBox = detection.boundingBox;
        result.classId = static_cast<int>(detection.type.length()); // 简单的ID生成
        results.push_back(result);

        // 用这个简化的版本来直接绘制
        Detection simple;
        simple.className = detection.description;
        simple.confidence = detection.confidence;
        simple.boundingBox = detection.boundingBox;
        simpleDetections.push_back(simple);

        qDebug() << "目标:" << detection.description
                 << "置信度:" << detection.confidence
                 << "边界框:" << detection.boundingBox;
    }

    // 尝试直接在视频帧上绘制并显示
    if (frameCapture && frameCapture->hasFrame()) {
        QImage currentFrame = frameCapture->getLatestFrame();
        QImage frameWithDetections = VideoFrameCapture::drawDetectionsOnFrame(currentFrame, simpleDetections);

        // 显示到主显示区域，确保正确缩放和居中
        if (detectionImageLabel) {
            QPixmap pixmap = QPixmap::fromImage(frameWithDetections);

            // 缩放到标签尺寸，保持宽高比，居中显示
            QPixmap scaledPixmap = pixmap.scaled(
                detectionImageLabel->size(),
                Qt::KeepAspectRatio,
                Qt::SmoothTransformation
                );

            detectionImageLabel->setPixmap(scaledPixmap);

            // 更新分辨率信息
            if (auto* resolutionLabel = findChild<QLabel*>()) {
                QString resInfo = QString("📺 16:9 宽屏显示 | %1x%2 | 🎯 检测: %3个目标")
                                      .arg(frameWithDetections.width())
                                      .arg(frameWithDetections.height())
                                      .arg(simpleDetections.size());

                // 查找分辨率标签并更新
                auto labels = findChildren<QLabel*>();
                for (auto* label : labels) {
                    if (label->text().contains("16:9") || label->text().contains("分辨率:")) {
                        label->setText(resInfo);
                        break;
                    }
                }
            }

            qDebug() << "在主区域显示带有检测结果的画面";
        }

        qDebug() << "直接在视屏帧上检测";
    }

    // 更新检测结果显示
    detectionOverlay->updateDetections(results);
    qDebug() << "更新后的检测图层包含" << results.size() << "个结果";

    // 更新状态栏
    if (!detections.empty()) {
        QStringList detectedItems;
        for (const auto& detection : detections) {
            // 避免QString::arg的格式化冲突，使用QString::number
            QString confidenceStr = QString::number(detection.confidence * 100, 'f', 1);
            QString item = detection.description + " (" + confidenceStr + "%)";
            detectedItems << item;
        }
        statusBar()->showMessage(QString("检测到: %1").arg(detectedItems.join(", ")));
    } else {
        statusBar()->showMessage("OpenCV处理中...");
    }
}

void MainWindow::onOpenCVStatsUpdated(const OpenCVVisionEngine::ProcessingStats& stats) {
    // 更新性能显示中的OpenCV统计信息
    QString perfText = QString(
                           "FPS: %1\n"
                           "延迟: %2ms\n"
                           "内存: %3MB\n"
                           "CPU: %4%\n"
                           "GPU: %5%\n"
                           "---OpenCV统计---\n"
                           "处理FPS: %6\n"
                           "平均耗时: %7ms\n"
                           "处理帧数: %8\n"
                           "检测数量: %9"
                           ).arg(camera && camera->isActive() ? "30.0" : "0.0")
                           .arg(camera && camera->isActive() ? "33" : "0")
                           .arg("512")
                           .arg("25.0")
                           .arg("15.0")
                           .arg(QString::number(1000.0f / stats.averageProcessingTime, 'f', 1))
                           .arg(QString::number(stats.averageProcessingTime, 'f', 1))
                           .arg(stats.processedFrames)
                           .arg(stats.detectionsCount);

    performanceText->setText(perfText);
}

QImage MainWindow::createTestImage() {
    // 创建测试图像，包含各种检测元素
    QImage testImage(640, 480, QImage::Format_RGB888);
    testImage.fill(QColor(50, 50, 50)); // 深灰色背景

    QPainter painter(&testImage);
    painter.setRenderHint(QPainter::Antialiasing);

    // 根据当前检测模式创建不同的测试内容
    OpenCVVisionEngine::VisionMode mode = openCVEngine->getVisionMode();

    switch (mode) {
    case OpenCVVisionEngine::FaceDetection: {
        // 创建更真实的人脸模拟
        QColor skinColor(220, 180, 140); // 肤色
        painter.setBrush(skinColor);
        painter.setPen(Qt::NoPen);

        // 绘制更大的椭圆形"人脸"
        painter.drawEllipse(150, 100, 180, 220); // 主要人脸

        // 添加面部特征
        painter.setBrush(Qt::black);
        painter.drawEllipse(190, 160, 15, 20); // 左眼
        painter.drawEllipse(265, 160, 15, 20); // 右眼

        painter.setBrush(QColor(200, 150, 120));
        painter.drawEllipse(225, 190, 20, 15); // 鼻子

        painter.setBrush(QColor(180, 100, 100));
        painter.drawEllipse(210, 230, 40, 15); // 嘴巴
        break;
    }

    case OpenCVVisionEngine::ColorDetection: {
        // 创建各种颜色的矩形
        painter.setBrush(Qt::red);
        painter.drawRect(50, 50, 80, 60);

        painter.setBrush(Qt::green);
        painter.drawRect(200, 100, 70, 70);

        painter.setBrush(Qt::blue);
        painter.drawRect(350, 150, 90, 50);

        painter.setBrush(Qt::yellow);
        painter.drawRect(450, 250, 60, 80);
        break;
    }

    case OpenCVVisionEngine::EdgeDetection: {
        // 创建有明显边缘的图形
        painter.setBrush(Qt::white);
        painter.setPen(QPen(Qt::black, 3));

        painter.drawRect(100, 100, 150, 100);
        painter.drawEllipse(300, 150, 120, 120);
        painter.drawPolygon(QPolygon({
            QPoint(450, 100),
            QPoint(500, 50),
            QPoint(550, 100),
            QPoint(525, 150),
            QPoint(475, 150)
        }));
        break;
    }

    default: {
        // 默认创建混合内容
        painter.setBrush(QColor(200, 150, 120));
        painter.drawEllipse(150, 100, 100, 120); // 肤色椭圆

        painter.setBrush(Qt::red);
        painter.drawRect(350, 150, 80, 60); // 红色矩形
        break;
    }
    }

    return testImage;
}

void MainWindow::setupFrameCapture() {
    // 创建视频帧捕获器
    frameCapture = new VideoFrameCapture(this);

    // 连接帧捕获信号 - 始终显示画面
    connect(frameCapture, &VideoFrameCapture::frameAvailable,
            this, [this](const QImage& frame) {
                // 始终显示摄像头画面
                if (detectionImageLabel && !frame.isNull()) {
                    // 如果正在进行检测，显示带检测结果的画面
                    if (isInferenceRunning && openCVEngine && openCVEngine->isProcessingEnabled()) {
                        // 检测模式：等待OpenCV处理结果
                        static int frameCount = 0;
                        // 收到200帧打印一次日志
                        if (++frameCount % 200 == 0) {
                            qDebug() << "检测帧数量达到" << frameCount << "size是:" << frame.size();
                        }
                    } else {
                        // 预览模式：直接显示原始画面，确保正确缩放和居中
                        QPixmap pixmap = QPixmap::fromImage(frame);

                        // 缩放到标签尺寸，保持宽高比，居中显示
                        QPixmap scaledPixmap = pixmap.scaled(
                            detectionImageLabel->size(),
                            Qt::KeepAspectRatio,
                            Qt::SmoothTransformation
                            );

                        detectionImageLabel->setPixmap(scaledPixmap);

                        // 更新分辨率信息
                        auto labels = findChildren<QLabel*>();
                        for (auto* label : labels) {
                            if (label->text().contains("16:9") || label->text().contains("等待摄像头")) {
                                QString resInfo = QString("📺 16:9 宽屏显示 | %1x%2 | 📹 实时预览")
                                                      .arg(frame.width())
                                                      .arg(frame.height());
                                label->setText(resInfo);
                                break;
                            }
                        }

                        static int frameCount = 0;
                        if (++frameCount % 200 == 0) {
                            qDebug() << "Displayed preview frame" << frameCount << "size:" << frame.size();
                        }
                    }
                }
            });

    connect(frameCapture, &VideoFrameCapture::captureStatusChanged,
            this, [this](bool enabled) {
                qDebug() << "检测帧状态改变为:" << enabled;
                if (enabled) {
                    statusBar()->showMessage("真实摄像头帧捕获已启用", 3000);
                }
            });

    qDebug() << "视频帧已初始化(摄像头启动时连接)";
}

// ==================== YOLO模型管理功能 ====================

void MainWindow::selectYOLOModel() {
    // 获取可用的YOLO模型列表
    QStringList availableModels = openCVEngine->getAvailableYOLOModels();

    if (availableModels.isEmpty()) {
        QMessageBox::information(this, "YOLO模型选择",
                                 "没有找到可用的YOLO模型文件。\n\n"
                                 "请确保在以下目录中有ONNX格式的YOLO模型：\n"
                                 "D:/AI_Models/yolo/\n\n"
                                 "支持的格式：.onnx, .pb, .weights");
        return;
    }

    // 显示当前模型
    QString currentModel = openCVEngine->getCurrentYOLOModel();
    QString currentModelName = currentModel.isEmpty() ? "无" : QFileInfo(currentModel).fileName();

    // 创建模型选择对话框
    bool ok;
    QStringList modelNames;
    for (const QString& modelPath : availableModels) {
        QFileInfo info(modelPath);
        QString displayName = QString("%1 (%2)")
                                  .arg(info.fileName())
                                  .arg(info.size() / (1024 * 1024)) + " MB";
        modelNames << displayName;
    }

    QString selectedModel = QInputDialog::getItem(this,
                                                  "YOLO模型选择",
                                                  QString("当前模型: %1\n\n选择新的YOLO模型:").arg(currentModelName),
                                                  modelNames, 0, false, &ok);

    if (ok && !selectedModel.isEmpty()) {
        int selectedIndex = modelNames.indexOf(selectedModel);
        if (selectedIndex >= 0 && selectedIndex < availableModels.size()) {
            QString modelPath = availableModels[selectedIndex];

            // 显示加载进度
            statusBar()->showMessage("正在加载YOLO模型...");
            QApplication::processEvents();

            // 加载模型
            if (openCVEngine->loadYOLOModel(modelPath)) {
                QMessageBox::information(this, "成功",
                                         QString("YOLO模型加载成功！\n\n模型: %1")
                                             .arg(QFileInfo(modelPath).fileName()));
                statusBar()->showMessage(QString("YOLO模型已更新: %1")
                                             .arg(QFileInfo(modelPath).fileName()), 5000);
            } else {
                QMessageBox::warning(this, "错误",
                                     QString("YOLO模型加载失败！\n\n模型: %1")
                                         .arg(QFileInfo(modelPath).fileName()));
                statusBar()->showMessage("YOLO模型加载失败", 5000);
            }
        }
    }
}

void MainWindow::configureYOLOParameters() {
    // 创建参数配置对话框
    QDialog dialog(this);
    dialog.setWindowTitle("YOLO检测参数配置");
    dialog.setModal(true);
    dialog.resize(400, 300);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 置信度阈值设置
    QGroupBox* confidenceGroup = new QGroupBox("置信度阈值", &dialog);
    QVBoxLayout* confidenceLayout = new QVBoxLayout(confidenceGroup);

    QLabel* confidenceLabel = new QLabel("当前值: 0.25", confidenceGroup);
    QSlider* confidenceSlider = new QSlider(Qt::Horizontal, confidenceGroup);
    confidenceSlider->setRange(10, 90);
    confidenceSlider->setValue(25);
    confidenceSlider->setTickPosition(QSlider::TicksBelow);
    confidenceSlider->setTickInterval(10);

    connect(confidenceSlider, &QSlider::valueChanged, [confidenceLabel](int value) {
        confidenceLabel->setText(QString("当前值: %1").arg(value / 100.0, 0, 'f', 2));
    });

    confidenceLayout->addWidget(confidenceLabel);
    confidenceLayout->addWidget(confidenceSlider);

    // NMS阈值设置
    QGroupBox* nmsGroup = new QGroupBox("非极大值抑制阈值", &dialog);
    QVBoxLayout* nmsLayout = new QVBoxLayout(nmsGroup);

    QLabel* nmsLabel = new QLabel("当前值: 0.45", nmsGroup);
    QSlider* nmsSlider = new QSlider(Qt::Horizontal, nmsGroup);
    nmsSlider->setRange(10, 90);
    nmsSlider->setValue(45);
    nmsSlider->setTickPosition(QSlider::TicksBelow);
    nmsSlider->setTickInterval(10);

    connect(nmsSlider, &QSlider::valueChanged, [nmsLabel](int value) {
        nmsLabel->setText(QString("当前值: %1").arg(value / 100.0, 0, 'f', 2));
    });

    nmsLayout->addWidget(nmsLabel);
    nmsLayout->addWidget(nmsSlider);

    // 按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* okButton = new QPushButton("确定", &dialog);
    QPushButton* cancelButton = new QPushButton("取消", &dialog);
    QPushButton* resetButton = new QPushButton("重置默认值", &dialog);

    connect(okButton, &QPushButton::clicked, &dialog, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);
    connect(resetButton, &QPushButton::clicked, [&]() {
        confidenceSlider->setValue(25);
        nmsSlider->setValue(45);
    });

    buttonLayout->addWidget(resetButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(okButton);
    buttonLayout->addWidget(cancelButton);

    layout->addWidget(confidenceGroup);
    layout->addWidget(nmsGroup);
    layout->addLayout(buttonLayout);

    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        float confidenceThreshold = confidenceSlider->value() / 100.0f;
        float nmsThreshold = nmsSlider->value() / 100.0f;

        openCVEngine->setYOLOConfidenceThreshold(confidenceThreshold);
        openCVEngine->setYOLONMSThreshold(nmsThreshold);

        QMessageBox::information(this, "参数已更新",
                                 QString("YOLO检测参数已更新：\n\n"
                                         "置信度阈值: %1\n"
                                         "NMS阈值: %2")
                                     .arg(confidenceThreshold, 0, 'f', 2)
                                     .arg(nmsThreshold, 0, 'f', 2));

        statusBar()->showMessage("YOLO参数已更新", 3000);
    }
}

void MainWindow::showYOLOModelInfo() {
    QString currentModel = openCVEngine->getCurrentYOLOModel();

    if (currentModel.isEmpty()) {
        QMessageBox::information(this, "YOLO模型信息",
                                 "当前没有加载YOLO模型。\n\n"
                                 "请先选择并加载一个YOLO模型。");
        return;
    }

    QFileInfo modelInfo(currentModel);

    // 获取模型文件信息
    QString modelName = modelInfo.fileName();
    QString modelPath = modelInfo.absoluteFilePath();
    qint64 modelSize = modelInfo.size();
    QString modelSizeStr = QString("%1 MB").arg(modelSize / (1024.0 * 1024.0), 0, 'f', 1);
    QDateTime lastModified = modelInfo.lastModified();

    // 获取可用模型列表
    QStringList availableModels = openCVEngine->getAvailableYOLOModels();

    // 创建信息对话框
    QDialog dialog(this);
    dialog.setWindowTitle("YOLO模型信息");
    dialog.setModal(true);
    dialog.resize(500, 400);

    QVBoxLayout* layout = new QVBoxLayout(&dialog);

    // 当前模型信息
    QGroupBox* currentGroup = new QGroupBox("当前模型", &dialog);
    QFormLayout* currentLayout = new QFormLayout(currentGroup);

    currentLayout->addRow("模型名称:", new QLabel(modelName));
    currentLayout->addRow("文件路径:", new QLabel(modelPath));
    currentLayout->addRow("文件大小:", new QLabel(modelSizeStr));
    currentLayout->addRow("修改时间:", new QLabel(lastModified.toString("yyyy-MM-dd hh:mm:ss")));

    // 检测参数信息
    QGroupBox* paramGroup = new QGroupBox("检测参数", &dialog);
    QFormLayout* paramLayout = new QFormLayout(paramGroup);

    paramLayout->addRow("置信度阈值:", new QLabel("0.25"));
    paramLayout->addRow("NMS阈值:", new QLabel("0.45"));
    paramLayout->addRow("输入尺寸:", new QLabel("640x640"));
    paramLayout->addRow("支持类别:", new QLabel("80个COCO类别"));

    // 可用模型列表
    QGroupBox* availableGroup = new QGroupBox("可用模型", &dialog);
    QVBoxLayout* availableLayout = new QVBoxLayout(availableGroup);

    QListWidget* modelList = new QListWidget(&dialog);
    for (const QString& modelPath : availableModels) {
        QFileInfo info(modelPath);
        QString displayText = QString("%1 (%2 MB)")
                                  .arg(info.fileName())
                                  .arg(info.size() / (1024.0 * 1024.0), 0, 'f', 1);

        QListWidgetItem* item = new QListWidgetItem(displayText);
        if (modelPath == currentModel) {
            item->setBackground(QColor(200, 255, 200)); // 高亮当前模型
            item->setText(displayText + " [当前]");
        }
        modelList->addItem(item);
    }

    availableLayout->addWidget(modelList);

    // 按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    QPushButton* selectButton = new QPushButton("选择模型", &dialog);
    QPushButton* configButton = new QPushButton("配置参数", &dialog);
    QPushButton* closeButton = new QPushButton("关闭", &dialog);

    connect(selectButton, &QPushButton::clicked, [this, &dialog]() {
        dialog.accept();
        selectYOLOModel();
    });

    connect(configButton, &QPushButton::clicked, [this, &dialog]() {
        dialog.accept();
        configureYOLOParameters();
    });

    connect(closeButton, &QPushButton::clicked, &dialog, &QDialog::accept);

    buttonLayout->addWidget(selectButton);
    buttonLayout->addWidget(configButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(closeButton);

    layout->addWidget(currentGroup);
    layout->addWidget(paramGroup);
    layout->addWidget(availableGroup);
    layout->addLayout(buttonLayout);

    dialog.exec();
}

// 上传图片并分析
void MainWindow::uploadAndAnalyzeImage() {
    // 打开文件选择对话框
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择要分析的图片 - AI图像识别平台",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.tiff *.gif);;所有文件 (*.*)"
        );

    if (fileName.isEmpty()) {
        return; // 用户取消了选择
    }

    // 加载图片
    QPixmap pixmap(fileName);
    if (pixmap.isNull()) {
        QMessageBox::warning(this, "错误", "无法加载选择的图片文件！");
        return;
    }

    // 更新状态栏
    QFileInfo fileInfo(fileName);
    statusBar()->showMessage(QString("已加载图片: %1 (%2x%3)")
                                 .arg(fileInfo.fileName())
                                 .arg(pixmap.width())
                                 .arg(pixmap.height()));

    // 显示图片在主显示区域
    // 缩放图片以适应显示区域，保持宽高比
    QPixmap scaledPixmap = pixmap.scaled(
        detectionImageLabel->size(),
        Qt::KeepAspectRatio,
        Qt::SmoothTransformation
        );
    detectionImageLabel->setPixmap(scaledPixmap);

    // 保存当前分析图像
    currentAnalysisImage = pixmap.toImage();

    // 隐藏摄像头相关显示，显示图片分析模式
    imageScrollArea->setVisible(false);

    // 询问用户是否要进行Gemini分析
    int ret = QMessageBox::question(
        this,
        "Gemini AI分析",
        QString("图片已加载成功！\n\n文件: %1\n分辨率: %2x%3\n\n是否要使用Gemini AI进行智能分析？")
            .arg(fileInfo.fileName())
            .arg(pixmap.width())
            .arg(pixmap.height()),
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::Yes
        );

    if (ret == QMessageBox::Yes) {
        // 调用Gemini分析方法
        analyzeImageWithGemini();
    }
}

// 使用YOLO分析图片
void MainWindow::analyzeImageWithYOLO() {
    // 检查是否有图片在显示
    QPixmap currentPixmap = detectionImageLabel->pixmap();
    if (currentPixmap.isNull()) {
        QMessageBox::warning(this, "错误", "没有可分析的图片！请先上传图片。");
        return;
    }

    // 创建进度对话框
    QProgressDialog progressDialog("正在使用YOLO分析图片...", "取消", 0, 100, this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setMinimumDuration(0);
    progressDialog.setValue(10);

    // 确保OpenCV引擎已初始化
    if (!openCVEngine) {
        progressDialog.close();
        QMessageBox::warning(this, "错误", "OpenCV引擎未初始化！");
        return;
    }

    progressDialog.setValue(20);

    // 设置为物体检测模式
    openCVEngine->setVisionMode(OpenCVVisionEngine::VisionMode::ObjectDetection);
    progressDialog.setValue(30);

    // 检查YOLO模型是否已加载
    if (openCVEngine->getCurrentYOLOModel().isEmpty()) {
        progressDialog.close();
        int ret = QMessageBox::question(
            this,
            "YOLO模型未加载",
            "YOLO模型尚未加载。是否要选择并加载YOLO模型？",
            QMessageBox::Yes | QMessageBox::No
            );

        if (ret == QMessageBox::Yes) {
            selectYOLOModel();
            // 重新检查是否加载成功
            if (openCVEngine->getCurrentYOLOModel().isEmpty()) {
                QMessageBox::warning(this, "错误", "YOLO模型加载失败，无法进行分析！");
                return;
            }
        } else {
            return;
        }

        // 重新显示进度对话框
        progressDialog.show();
        progressDialog.setValue(40);
    }

    progressDialog.setValue(50);

    // 将QPixmap转换为QImage，然后转换为OpenCV Mat
    QImage qImage = currentPixmap.toImage();
    progressDialog.setValue(60);

    // 处理图像并获取检测结果
    std::vector<OpenCVDetection> detections = openCVEngine->processFrame(qImage);
    progressDialog.setValue(80);

    // 在图像上绘制检测框和标签
    QImage resultImage = qImage.copy();
    QPainter painter(&resultImage);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制检测结果
    for (const auto& detection : detections) {
        // 计算绝对坐标
        int x = static_cast<int>(detection.boundingBox.x() * resultImage.width());
        int y = static_cast<int>(detection.boundingBox.y() * resultImage.height());
        int width = static_cast<int>(detection.boundingBox.width() * resultImage.width());
        int height = static_cast<int>(detection.boundingBox.height() * resultImage.height());

        // 绘制边界框
        painter.setPen(QPen(Qt::red, 3));
        painter.drawRect(x, y, width, height);

        // 绘制标签背景
        QString label = QString("%1 (%.1f%%)").arg(detection.type).arg(detection.confidence * 100);
        QFont font = painter.font();
        font.setPointSize(12);
        font.setBold(true);
        painter.setFont(font);

        QFontMetrics metrics(font);
        QRect textRect = metrics.boundingRect(label);
        textRect.adjust(-4, -2, 4, 2);
        textRect.moveTo(x, y - textRect.height());

        painter.fillRect(textRect, QBrush(Qt::red));
        painter.setPen(QPen(Qt::white));
        painter.drawText(textRect, Qt::AlignCenter, label);
    }

    progressDialog.setValue(90);

    // 显示分析结果
    QPixmap resultPixmap = QPixmap::fromImage(resultImage);
    QPixmap scaledResult = resultPixmap.scaled(
        detectionImageLabel->size(),
        Qt::KeepAspectRatio,
        Qt::SmoothTransformation
        );
    detectionImageLabel->setPixmap(scaledResult);

    progressDialog.setValue(100);
    progressDialog.close();

    // 显示分析结果统计信息
    QString statsText = QString("YOLO分析完成！\n\n检测到 %1 个物体：\n").arg(detections.size());
    QMap<QString, int> classCount;

    for (const auto& detection : detections) {
        classCount[detection.type]++;
    }

    for (auto it = classCount.begin(); it != classCount.end(); ++it) {
        statsText += QString("• %1: %2个\n").arg(it.key()).arg(it.value());
    }

    QMessageBox::information(this, "分析结果", statsText);

    // 询问用户是否保存分析结果图像
    int saveRet = QMessageBox::question(
        this,
        "保存结果",
        "是否要保存带有检测结果的图片？",
        QMessageBox::Yes | QMessageBox::No
        );

    if (saveRet == QMessageBox::Yes) {
        QString saveFileName = QFileDialog::getSaveFileName(
            this,
            "保存分析结果",
            QStandardPaths::writableLocation(QStandardPaths::PicturesLocation) + "/yolo_analysis_result.png",
            "PNG图片 (*.png);;JPEG图片 (*.jpg);;所有文件 (*.*)"
            );

        if (!saveFileName.isEmpty()) {
            if (resultImage.save(saveFileName)) {
                statusBar()->showMessage(QString("分析结果已保存: %1").arg(QFileInfo(saveFileName).fileName()), 3000);
            } else {
                QMessageBox::warning(this, "错误", "保存图片失败！");
            }
        }
    }
}

// 批量分析图片
void MainWindow::batchAnalyzeImages() {
    // 选择多个图片文件
    QStringList fileNames = QFileDialog::getOpenFileNames(
        this,
        "选择要批量分析的图片 - AI图像识别平台",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.tiff *.gif);;所有文件 (*.*)"
        );

    if (fileNames.isEmpty()) {
        return; // 用户取消了选择
    }

    // 确保OpenCV引擎已初始化
    if (!openCVEngine) {
        QMessageBox::warning(this, "错误", "OpenCV引擎未初始化！");
        return;
    }

    // 检查YOLO模型是否已加载
    if (openCVEngine->getCurrentYOLOModel().isEmpty()) {
        int ret = QMessageBox::question(
            this,
            "YOLO模型未加载",
            "YOLO模型尚未加载。是否要选择并加载YOLO模型？",
            QMessageBox::Yes | QMessageBox::No
            );

        if (ret == QMessageBox::Yes) {
            selectYOLOModel();
            // 重新检查是否加载成功
            if (openCVEngine->getCurrentYOLOModel().isEmpty()) {
                QMessageBox::warning(this, "错误", "YOLO模型加载失败，无法进行批量分析！");
                return;
            }
        } else {
            return;
        }
    }

    // 设置为物体检测模式
    openCVEngine->setVisionMode(OpenCVVisionEngine::VisionMode::ObjectDetection);

    // 让用户选择结果保存目录
    QString saveDir = QFileDialog::getExistingDirectory(
        this,
        "选择分析结果保存目录",
        QStandardPaths::writableLocation(QStandardPaths::PicturesLocation)
        );

    if (saveDir.isEmpty()) {
        return; // 用户取消了选择
    }

    // 创建进度对话框
    QProgressDialog progressDialog("正在批量分析图片...", "取消", 0, fileNames.size(), this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setMinimumDuration(0);

    int successCount = 0;
    int failCount = 0;
    QStringList processedFiles;

    // 逐个处理图片
    for (int i = 0; i < fileNames.size(); ++i) {
        if (progressDialog.wasCanceled()) {
            break;
        }

        const QString& fileName = fileNames[i];
        QFileInfo fileInfo(fileName);

        progressDialog.setLabelText(QString("正在分析: %1 (%2/%3)")
                                        .arg(fileInfo.fileName())
                                        .arg(i + 1)
                                        .arg(fileNames.size()));
        progressDialog.setValue(i);

        // 加载图片
        QImage image(fileName);
        if (image.isNull()) {
            failCount++;
            continue;
        }

        // 处理图像并获取检测结果
        std::vector<OpenCVDetection> detections = openCVEngine->processFrame(image);

        // 在图像上绘制检测框和标签
        QImage resultImage = image.copy();
        QPainter painter(&resultImage);
        painter.setRenderHint(QPainter::Antialiasing);

        // 绘制检测结果
        for (const auto& detection : detections) {
            // 计算绝对坐标
            int x = static_cast<int>(detection.boundingBox.x() * resultImage.width());
            int y = static_cast<int>(detection.boundingBox.y() * resultImage.height());
            int width = static_cast<int>(detection.boundingBox.width() * resultImage.width());
            int height = static_cast<int>(detection.boundingBox.height() * resultImage.height());

            // 绘制边界框
            painter.setPen(QPen(Qt::red, 3));
            painter.drawRect(x, y, width, height);

            // 绘制标签背景
            QString label = QString("%1 (%.1f%%)").arg(detection.type).arg(detection.confidence * 100);
            QFont font = painter.font();
            font.setPointSize(12);
            font.setBold(true);
            painter.setFont(font);

            QFontMetrics metrics(font);
            QRect textRect = metrics.boundingRect(label);
            textRect.adjust(-4, -2, 4, 2);
            textRect.moveTo(x, y - textRect.height());

            painter.fillRect(textRect, QBrush(Qt::red));
            painter.setPen(QPen(Qt::white));
            painter.drawText(textRect, Qt::AlignCenter, label);
        }

        // 保存分析结果
        QString baseName = fileInfo.completeBaseName();
        QString extension = fileInfo.suffix();
        QString resultFileName = QString("%1/%2_yolo_analysis.%3")
                                     .arg(saveDir)
                                     .arg(baseName)
                                     .arg(extension.isEmpty() ? "png" : extension);

        if (resultImage.save(resultFileName)) {
            successCount++;
            processedFiles.append(resultFileName);
        } else {
            failCount++;
        }

        // 让界面有机会更新
        QApplication::processEvents();
    }

    progressDialog.setValue(fileNames.size());
    progressDialog.close();

    // 显示批处理完成统计信息
    QString statsText = QString("批量分析完成！\n\n")
                        + QString("总文件数: %1\n").arg(fileNames.size())
                        + QString("成功处理: %2\n").arg(successCount)
                        + QString("处理失败: %3\n").arg(failCount)
                        + QString("保存目录: %4").arg(saveDir);

    QMessageBox::information(this, "批量分析完成", statsText);

    // 更新状态栏
    statusBar()->showMessage(QString("批量分析完成: %1/%2 成功").arg(successCount).arg(fileNames.size()), 5000);
}

// ==================== Gemini API管理功能 ====================

void MainWindow::setupGeminiAPI() {
    // 创建Gemini API管理器
    geminiManager = new GeminiAPIManager(this);

    // 连接Gemini信号
    connect(geminiManager, &GeminiAPIManager::analysisCompleted,
            this, &MainWindow::onGeminiAnalysisCompleted);
    connect(geminiManager, &GeminiAPIManager::analysisError,
            this, &MainWindow::onGeminiAnalysisError);
    connect(geminiManager, &GeminiAPIManager::analysisProgress,
            this, &MainWindow::onGeminiAnalysisProgress);

    qDebug() << "Gemini API manager initialized";
}

void MainWindow::configureGeminiAPI() {
    // 创建API密钥配置对话框
    QDialog dialog(this);
    dialog.setWindowTitle("Gemini API配置");
    dialog.setModal(true);
    dialog.resize(500, 300);

    auto* layout = new QVBoxLayout(&dialog);

    // 说明文本
    auto* infoLabel = new QLabel(
        "请输入您的Gemini API密钥。您可以从Google AI Studio获取API密钥：\n"
        "https://aistudio.google.com/app/apikey\n\n"
        "API密钥将安全地保存在本地配置中。"
        );
    infoLabel->setWordWrap(true);
    infoLabel->setStyleSheet("QLabel { color: #666; margin: 10px; }");
    layout->addWidget(infoLabel);

    // API密钥输入
    auto* keyLabel = new QLabel("API密钥:");
    layout->addWidget(keyLabel);

    auto* keyEdit = new QLineEdit();
    keyEdit->setPlaceholderText("输入您的Gemini API密钥...");
    keyEdit->setText(geminiManager->getApiKey());
    keyEdit->setEchoMode(QLineEdit::Password);
    layout->addWidget(keyEdit);

    // 显示/隐藏密钥按钮
    auto* showKeyButton = new QPushButton("显示密钥");
    connect(showKeyButton, &QPushButton::clicked, [keyEdit, showKeyButton]() {
        if (keyEdit->echoMode() == QLineEdit::Password) {
            keyEdit->setEchoMode(QLineEdit::Normal);
            showKeyButton->setText("隐藏密钥");
        } else {
            keyEdit->setEchoMode(QLineEdit::Password);
            showKeyButton->setText("显示密钥");
        }
    });
    layout->addWidget(showKeyButton);

    // 测试连接按钮
    auto* testButton = new QPushButton("测试API连接");
    testButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #1976D2; }");
    layout->addWidget(testButton);

    // 按钮区域
    auto* buttonLayout = new QHBoxLayout();
    auto* saveButton = new QPushButton("保存");
    auto* cancelButton = new QPushButton("取消");

    saveButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #45a049; }");
    cancelButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #d32f2f; }");

    buttonLayout->addWidget(saveButton);
    buttonLayout->addWidget(cancelButton);
    layout->addLayout(buttonLayout);

    // 连接按钮信号
    connect(saveButton, &QPushButton::clicked, [&dialog, keyEdit, this]() {
        QString apiKey = keyEdit->text().trimmed();
        if (!apiKey.isEmpty()) {
            geminiManager->setApiKey(apiKey);
            QMessageBox::information(&dialog, "成功", "API密钥已保存！");
            dialog.accept();
        } else {
            QMessageBox::warning(&dialog, "错误", "请输入有效的API密钥！");
        }
    });

    connect(cancelButton, &QPushButton::clicked, &dialog, &QDialog::reject);

    // 测试连接功能（简化版）
    connect(testButton, &QPushButton::clicked, [keyEdit, this]() {
        QString apiKey = keyEdit->text().trimmed();
        if (apiKey.isEmpty()) {
            QMessageBox::warning(this, "错误", "请先输入API密钥！");
            return;
        }

        // 简单验证API密钥格式
        if (apiKey.length() < 20) {
            QMessageBox::warning(this, "错误", "API密钥格式不正确！");
            return;
        }

        QMessageBox::information(this, "测试", "API密钥格式正确。实际连接测试需要上传图片进行验证。");
    });

    dialog.exec();
}

void MainWindow::showGeminiAPIInfo() {
    QString apiKeyStatus = geminiManager->hasValidApiKey() ? "已配置" : "未配置";
    QString currentKey = geminiManager->getApiKey();
    QString maskedKey = currentKey.isEmpty() ? "无" :
                            (currentKey.left(8) + "..." + currentKey.right(4));

    QString infoText = QString(
                           "=== Gemini API信息 ===\n\n"
                           "API状态: %1\n"
                           "API密钥: %2\n\n"
                           "支持的功能:\n"
                           "• 图像内容分析\n"
                           "• 物体识别\n"
                           "• 场景描述\n"
                           "• 情感分析\n"
                           "• 详细文本描述\n\n"
                           "使用说明:\n"
                           "1. 配置有效的Gemini API密钥\n"
                           "2. 上传图片进行分析\n"
                           "3. 查看详细的AI分析结果\n\n"
                           "获取API密钥:\n"
                           "访问 https://aistudio.google.com/app/apikey"
                           ).arg(apiKeyStatus).arg(maskedKey);

    QMessageBox::information(this, "Gemini API信息", infoText);
}

// 使用Gemini分析图片
void MainWindow::analyzeImageWithGemini() {
    // 检查API密钥
    if (!geminiManager->hasValidApiKey()) {
        QMessageBox::warning(this, "API密钥未配置",
                             "请先配置Gemini API密钥。\n\n"
                             "您可以通过菜单 '视觉模式' -> '配置Gemini API' 进行配置。");
        return;
    }

    // 检查是否有图片在显示
    if (currentAnalysisImage.isNull()) {
        QMessageBox::warning(this, "错误", "没有可分析的图片！请先上传图片。");
        return;
    }

    // 询问用户选择分析类型
    QStringList analysisTypes;
    analysisTypes << "基础分析" << "详细分析" << "自定义提示词";

    bool ok;
    QString selectedType = QInputDialog::getItem(this,
                                                 "选择分析类型 - Gemini AI",
                                                 "请选择分析类型:",
                                                 analysisTypes, 0, false, &ok);

    if (!ok) {
        return;
    }

    QString customPrompt;
    if (selectedType == "自定义提示词") {
        customPrompt = QInputDialog::getText(this,
                                             "自定义提示词 - Gemini AI",
                                             "请输入您的分析提示词:",
                                             QLineEdit::Normal, "", &ok);

        if (!ok || customPrompt.trimmed().isEmpty()) {
            return;
        }
    }

    // 开始分析
    statusBar()->showMessage("正在使用Gemini AI分析图片...");

    if (selectedType == "自定义提示词") {
        geminiManager->analyzeImageWithCustomPrompt(currentAnalysisImage, customPrompt);
    } else if (selectedType == "详细分析") {
        // 使用详细分析提示词
        QString detailedPrompt = "作为一个专业的图像分析师，请对这张图片进行全面分析：\n\n"
                                 "**物体识别：**\n"
                                 "- 列出图片中所有可识别的物体、动物、人物\n"
                                 "- 描述它们的位置、大小、状态\n\n"
                                 "**场景分析：**\n"
                                 "- 确定场景类型（室内/室外、具体环境）\n"
                                 "- 描述背景和环境特征\n\n"
                                 "**视觉元素：**\n"
                                 "- 主要颜色和色调\n"
                                 "- 光线条件和阴影\n"
                                 "- 构图和视角\n\n"
                                 "**情感和氛围：**\n"
                                 "- 图片传达的情感或氛围\n"
                                 "- 人物表情（如有）\n\n"
                                 "请用中文详细回答，条理清晰。";
        geminiManager->analyzeImageWithCustomPrompt(currentAnalysisImage, detailedPrompt);
    } else {
        // 基础分析
        geminiManager->analyzeImage(currentAnalysisImage);
    }
}

// Gemini分析结果处理
void MainWindow::onGeminiAnalysisCompleted(const GeminiAnalysisResult& result) {
    statusBar()->showMessage("Gemini分析完成！", 3000);

    // 创建结果显示对话框
    QDialog resultDialog(this);
    resultDialog.setWindowTitle("Gemini AI分析结果");
    resultDialog.setModal(true);
    resultDialog.resize(800, 600);

    auto* layout = new QVBoxLayout(&resultDialog);

    // 分析结果文本
    auto* resultText = new QTextEdit();
    resultText->setPlainText(result.description);
    resultText->setReadOnly(true);
    resultText->setFont(QFont("Microsoft YaHei", 10));
    layout->addWidget(resultText);

    // 统计信息
    if (!result.detectedObjects.isEmpty() || !result.colors.isEmpty()) {
        auto* statsLabel = new QLabel();
        QString statsText = "检测统计：\n";

        if (!result.detectedObjects.isEmpty()) {
            statsText += QString("识别物体: %1\n").arg(result.detectedObjects.join(", "));
        }

        if (!result.colors.isEmpty()) {
            statsText += QString("主要颜色: %1\n").arg(result.colors.join(", "));
        }

        statsLabel->setText(statsText);
        statsLabel->setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }");
        layout->addWidget(statsLabel);
    }

    // 按钮区域
    auto* buttonLayout = new QHBoxLayout();

    auto* copyButton = new QPushButton("复制结果");
    copyButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #1976D2; }");
    connect(copyButton, &QPushButton::clicked, [result]() {
        QApplication::clipboard()->setText(result.description);
    });

    auto* saveButton = new QPushButton("保存结果");
    saveButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #45a049; }");
    connect(saveButton, &QPushButton::clicked, [this, result]() {
        QString fileName = QFileDialog::getSaveFileName(this,
                                                        "保存分析结果",
                                                        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/gemini_analysis.txt",
                                                        "文本文件 (*.txt)");

        if (!fileName.isEmpty()) {
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out.setEncoding(QStringConverter::Utf8);
                out << "=== Gemini AI图像分析结果 ===\n\n";
                out << "分析时间: " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n\n";
                out << result.description;

                QMessageBox::information(this, "保存成功", "分析结果已保存到: " + fileName);
            } else {
                QMessageBox::warning(this, "保存失败", "无法保存文件: " + fileName);
            }
        }
    });

    auto* closeButton = new QPushButton("关闭");
    closeButton->setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #d32f2f; }");
    connect(closeButton, &QPushButton::clicked, &resultDialog, &QDialog::accept);

    buttonLayout->addWidget(copyButton);
    buttonLayout->addWidget(saveButton);
    buttonLayout->addWidget(closeButton);
    layout->addLayout(buttonLayout);

    resultDialog.exec();
}

void MainWindow::onGeminiAnalysisError(const QString& errorMessage) {
    statusBar()->showMessage("Gemini分析失败", 3000);

    QString fullMessage = "Gemini AI分析失败：\n\n" + errorMessage +
                          "\n\n请检查：\n"
                          "1. 网络连接是否正常\n"
                          "2. API密钥是否有效\n"
                          "3. 图片格式是否支持";

    QMessageBox::warning(this, "分析失败", fullMessage);
}

void MainWindow::onGeminiAnalysisProgress(const QString& status) {
    statusBar()->showMessage(status);
}
