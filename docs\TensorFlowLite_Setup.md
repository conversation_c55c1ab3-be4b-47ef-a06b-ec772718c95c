# TensorFlow Lite C++ 集成指南

本文档介绍如何在项目中集成TensorFlow Lite C++库，以支持图像分类和目标检测功能。

## 1. TensorFlow Lite 简介

TensorFlow Lite是TensorFlow的轻量级解决方案，专为移动和嵌入式设备设计。它支持：
- 图像分类
- 目标检测
- 语义分割
- 自然语言处理
- 等多种机器学习任务

## 2. 安装 TensorFlow Lite C++ 库

### 2.1 Windows 平台

#### 方法一：预编译库（推荐）

1. **下载预编译库**
   ```bash
   # 从官方GitHub下载预编译的Windows库
   # 或者从以下链接下载：
   # https://github.com/tensorflow/tensorflow/releases
   ```

2. **解压到指定目录**
   ```
   D:/tensorflow_lite/
   ├── include/
   │   └── tensorflow/
   │       └── lite/
   │           ├── interpreter.h
   │           ├── model.h
   │           └── ...
   └── lib/
       ├── tensorflowlite.lib
       └── tensorflowlite.dll
   ```

#### 方法二：从源码编译

1. **安装依赖**
   - Visual Studio 2019 或更新版本
   - CMake 3.16+
   - Python 3.7+

2. **克隆TensorFlow源码**
   ```bash
   git clone https://github.com/tensorflow/tensorflow.git
   cd tensorflow
   ```

3. **配置编译**
   ```bash
   # 配置编译选项
   python configure.py
   
   # 编译TensorFlow Lite
   bazel build //tensorflow/lite:tensorflowlite
   ```

4. **安装到系统**
   ```bash
   # 复制头文件
   mkdir -p D:/tensorflow_lite/include
   cp -r tensorflow/lite D:/tensorflow_lite/include/tensorflow/
   
   # 复制库文件
   mkdir -p D:/tensorflow_lite/lib
   cp bazel-bin/tensorflow/lite/libtensorflowlite.so D:/tensorflow_lite/lib/
   ```

### 2.2 Linux 平台

#### 使用包管理器安装

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install libtensorflowlite-dev

# CentOS/RHEL
sudo yum install tensorflow-lite-devel
```

#### 从源码编译

```bash
# 安装依赖
sudo apt-get install build-essential cmake python3-dev

# 克隆源码
git clone https://github.com/tensorflow/tensorflow.git
cd tensorflow

# 编译
mkdir build && cd build
cmake ../tensorflow/lite
make -j$(nproc)

# 安装
sudo make install
```

## 3. 项目配置

### 3.1 CMake 配置

项目已经配置了自动检测TensorFlow Lite的CMake脚本：

```cmake
# 启用TensorFlow Lite支持
option(ENABLE_TENSORFLOW_LITE "Enable TensorFlow Lite support" ON)

# 自动查找TensorFlow Lite
find_path(TENSORFLOW_LITE_INCLUDE_DIR ...)
find_library(TENSORFLOW_LITE_LIB ...)
```

### 3.2 qmake 配置

使用qmake编译时，需要启用TensorFlow Lite支持：

```bash
# 启用TensorFlow Lite支持
qmake CONFIG+=enable_tensorflow_lite

# 编译项目
make
```

### 3.3 环境变量设置

如果TensorFlow Lite安装在非标准位置，可以设置环境变量：

```bash
# Windows
set TENSORFLOW_LITE_ROOT=D:/tensorflow_lite

# Linux/macOS
export TENSORFLOW_LITE_ROOT=/usr/local/tensorflow_lite
```

## 4. 验证安装

### 4.1 编译测试

编译项目时应该看到类似输出：

```
-- TensorFlow Lite found:
--     include: D:/tensorflow_lite/include
--     library: D:/tensorflow_lite/lib/tensorflowlite.lib
-- TensorFlow Lite library linked successfully
```

### 4.2 运行时测试

启动应用程序，在控制台应该看到：

```
TensorFlow Lite Engine initialized
TensorFlow Lite Model Manager initialized
```

## 5. 模型准备

### 5.1 获取预训练模型

1. **图像分类模型**
   ```bash
   # MobileNet V1
   wget https://storage.googleapis.com/download.tensorflow.org/models/mobilenet_v1_2018_02_22/mobilenet_v1_1.0_224_quant.tgz
   
   # 解压到 models/ 目录
   tar -xzf mobilenet_v1_1.0_224_quant.tgz -C models/
   ```

2. **目标检测模型**
   ```bash
   # COCO SSD MobileNet
   wget https://storage.googleapis.com/download.tensorflow.org/models/tflite/coco_ssd_mobilenet_v1_1.0_quant_2018_06_29.zip
   
   # 解压到 models/ 目录
   unzip coco_ssd_mobilenet_v1_1.0_quant_2018_06_29.zip -d models/
   ```

### 5.2 标签文件

确保为每个模型准备对应的标签文件：

```
models/
├── mobilenet_v1_224_quant.tflite
├── imagenet_labels.txt
├── coco_ssd_mobilenet.tflite
└── coco_labels.txt
```

## 6. 使用示例

### 6.1 基本使用

```cpp
#include "TensorFlowLiteEngine.h"
#include "TensorFlowLiteModelManager.h"

// 创建引擎和模型管理器
TensorFlowLiteEngine* engine = new TensorFlowLiteEngine(this);
TensorFlowLiteModelManager* modelManager = new TensorFlowLiteModelManager(this);

// 扫描可用模型
modelManager->scanModels();

// 加载默认分类模型
TFLiteModelInfo classificationModel = modelManager->getDefaultModel(
    TensorFlowLiteEngine::ImageClassification);

if (!classificationModel.name.isEmpty()) {
    engine->loadModel(classificationModel.path, classificationModel.type);
    if (!classificationModel.labelsPath.isEmpty()) {
        engine->loadLabels(classificationModel.labelsPath);
    }
}

// 进行推理
QImage image("test_image.jpg");
TFLiteInferenceResult result = engine->classifyImage(image);

if (result.success) {
    qDebug() << "推理成功，耗时:" << result.inferenceTime << "ms";
    for (const auto& detection : result.detections) {
        qDebug() << "类别:" << detection.className 
                 << "置信度:" << detection.confidence;
    }
}
```

### 6.2 异步推理

```cpp
// 连接信号
connect(engine, &TensorFlowLiteEngine::inferenceCompleted,
        this, &MainWindow::onInferenceCompleted);

// 异步推理（在后台线程中执行）
QtConcurrent::run([=]() {
    TFLiteInferenceResult result = engine->runInference(image);
    // 结果通过信号返回
});
```

## 7. 故障排除

### 7.1 常见问题

1. **找不到TensorFlow Lite库**
   - 检查安装路径是否正确
   - 确认环境变量设置
   - 验证库文件权限

2. **编译错误**
   - 检查编译器版本兼容性
   - 确认CMake版本
   - 检查依赖库是否完整

3. **运行时错误**
   - 检查模型文件格式
   - 验证输入数据格式
   - 确认内存分配

### 7.2 调试技巧

1. **启用详细日志**
   ```cpp
   // 在main函数中添加
   QLoggingCategory::setFilterRules("*.debug=true");
   ```

2. **检查模型信息**
   ```cpp
   // 加载模型后检查
   if (engine->isModelLoaded()) {
       qDebug() << "模型类型:" << engine->getModelType();
       qDebug() << "类别数量:" << engine->getClassNames().size();
   }
   ```

## 8. 性能优化

### 8.1 硬件加速

```cpp
// 启用GPU加速（如果支持）
engine->setAccelerationType(TensorFlowLiteEngine::GPU);

// 或者使用NNAPI（Android）
engine->setAccelerationType(TensorFlowLiteEngine::NNAPI);
```

### 8.2 推理参数调优

```cpp
// 调整置信度阈值
engine->setConfidenceThreshold(0.7f);

// 调整NMS阈值（目标检测）
engine->setNMSThreshold(0.5f);

// 限制最大检测数量
engine->setMaxDetections(20);
```

## 9. 扩展功能

### 9.1 自定义模型

项目支持加载自定义训练的TensorFlow Lite模型：

1. 将模型文件放入 `models/` 目录
2. 准备对应的标签文件
3. 重新扫描模型：`modelManager->scanModels()`

### 9.2 模型下载

支持从网络下载预训练模型：

```cpp
// 下载模型
modelManager->downloadModel("custom_model", "https://example.com/model.tflite");

// 监听下载进度
connect(modelManager, &TensorFlowLiteModelManager::modelDownloadProgress,
        this, &MainWindow::onDownloadProgress);
```

## 10. 参考资源

- [TensorFlow Lite 官方文档](https://www.tensorflow.org/lite)
- [TensorFlow Lite C++ API](https://www.tensorflow.org/lite/api_docs/cc)
- [预训练模型库](https://www.tensorflow.org/lite/models)
- [模型优化指南](https://www.tensorflow.org/lite/performance/model_optimization)
