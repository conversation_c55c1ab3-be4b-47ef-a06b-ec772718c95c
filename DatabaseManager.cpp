#include "DatabaseManager.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QSqlRecord>
#include <QSqlField>
#include <QFile>
#include <QFileInfo>
#include <QMutexLocker>
#include <QUuid>
#include <QJsonArray>

#if defined(_MSC_VER)
#pragma execution_character_set("utf-8")
#endif

DatabaseManager::DatabaseManager(QObject* parent)
    : QObject(parent)
    , connected_(false)
{
    qDebug() << "数据库管理器已创建";
}

DatabaseManager::~DatabaseManager()
{
    close();
    qDebug() << "数据库管理器已销毁";
}

bool DatabaseManager::initialize(const QString& databasePath)
{
    QMutexLocker locker(&mutex_);
    
    if (connected_) {
        qDebug() << "数据库已连接";
        return true;
    }
    
    // 确定数据库路径
    if (databasePath.isEmpty()) {
        QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        QDir().mkpath(appDataPath);
        databasePath_ = QDir(appDataPath).filePath("ai_vision_platform.db");
    } else {
        databasePath_ = databasePath;
        QDir().mkpath(QFileInfo(databasePath_).absolutePath());
    }
    
    // 创建数据库连接
    QString connectionName = generateConnectionName();
    database_ = QSqlDatabase::addDatabase("QSQLITE", connectionName);
    database_.setDatabaseName(databasePath_);
    
    if (!database_.open()) {
        QString error = QString("无法打开数据库: %1").arg(database_.lastError().text());
        qDebug() << error;
        emit databaseError(error);
        return false;
    }
    
    // 启用外键约束
    QSqlQuery query(database_);
    query.exec("PRAGMA foreign_keys = ON");
    
    // 创建表结构
    if (!createTables()) {
        QString error = "创建数据库表失败";
        qDebug() << error;
        emit databaseError(error);
        close();
        return false;
    }
    
    // 创建索引
    if (!createIndexes()) {
        qDebug() << "创建索引失败，但不影响基本功能";
    }
    
    connected_ = true;
    emit connectionStateChanged(true);
    
    qDebug() << "数据库初始化成功:" << databasePath_;
    return true;
}

bool DatabaseManager::isConnected() const
{
    QMutexLocker locker(&mutex_);
    return connected_ && database_.isOpen();
}

void DatabaseManager::close()
{
    QMutexLocker locker(&mutex_);
    
    if (database_.isOpen()) {
        database_.close();
    }
    
    if (connected_) {
        connected_ = false;
        emit connectionStateChanged(false);
    }
    
    qDebug() << "数据库连接已关闭";
}

int DatabaseManager::addDetectionRecord(const DetectionRecord& record)
{
    QMutexLocker locker(&mutex_);
    
    if (!connected_) {
        qDebug() << "数据库未连接";
        return -1;
    }
    
    QSqlQuery query = prepareQuery(
        "INSERT INTO detection_records "
        "(session_id, timestamp, image_hash, image_path, model_name, model_type, "
        "engine_type, detection_results, processing_time, confidence, detection_count, notes) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    );
    
    query.addBindValue(record.sessionId);
    query.addBindValue(record.timestamp);
    query.addBindValue(record.imageHash);
    query.addBindValue(record.imagePath);
    query.addBindValue(record.modelName);
    query.addBindValue(record.modelType);
    query.addBindValue(record.engineType);
    query.addBindValue(QJsonDocument(record.detectionResults).toJson(QJsonDocument::Compact));
    query.addBindValue(record.processingTime);
    query.addBindValue(record.confidence);
    query.addBindValue(record.detectionCount);
    query.addBindValue(record.notes);
    
    if (!query.exec()) {
        logError("添加识别记录", query.lastError());
        return -1;
    }
    
    int recordId = query.lastInsertId().toInt();
    emit recordAdded("detection_records", recordId);
    
    qDebug() << "添加识别记录成功，ID:" << recordId;
    return recordId;
}

DetectionRecord DatabaseManager::getDetectionRecord(int id) const
{
    QMutexLocker locker(&mutex_);
    
    DetectionRecord record;
    
    if (!connected_) {
        return record;
    }
    
    QSqlQuery query = prepareQuery(
        "SELECT * FROM detection_records WHERE id = ?"
    );
    query.addBindValue(id);
    
    if (query.exec() && query.next()) {
        record = recordFromQuery(query);
    } else {
        logError("获取识别记录", query.lastError());
    }
    
    return record;
}

QList<DetectionRecord> DatabaseManager::getDetectionRecords(int limit, int offset) const
{
    QMutexLocker locker(&mutex_);
    
    QList<DetectionRecord> records;
    
    if (!connected_) {
        return records;
    }
    
    QSqlQuery query = prepareQuery(
        "SELECT * FROM detection_records ORDER BY timestamp DESC LIMIT ? OFFSET ?"
    );
    query.addBindValue(limit);
    query.addBindValue(offset);
    
    if (query.exec()) {
        while (query.next()) {
            records.append(recordFromQuery(query));
        }
    } else {
        logError("获取识别记录列表", query.lastError());
    }
    
    return records;
}

QList<DetectionRecord> DatabaseManager::queryDetectionRecords(const QVariantMap& conditions, 
                                                             int limit, int offset) const
{
    QMutexLocker locker(&mutex_);
    
    QList<DetectionRecord> records;
    
    if (!connected_) {
        return records;
    }
    
    QString whereClause;
    QVariantList bindValues;
    
    // 构建WHERE子句
    QStringList clauses;
    for (auto it = conditions.begin(); it != conditions.end(); ++it) {
        clauses.append(QString("%1 = ?").arg(it.key()));
        bindValues.append(it.value());
    }
    
    if (!clauses.isEmpty()) {
        whereClause = "WHERE " + clauses.join(" AND ");
    }
    
    QString queryString = QString(
        "SELECT * FROM detection_records %1 ORDER BY timestamp DESC LIMIT ? OFFSET ?"
    ).arg(whereClause);
    
    QSqlQuery query = prepareQuery(queryString);
    
    // 绑定条件值
    for (const QVariant& value : bindValues) {
        query.addBindValue(value);
    }
    
    // 绑定分页参数
    query.addBindValue(limit);
    query.addBindValue(offset);
    
    if (query.exec()) {
        while (query.next()) {
            records.append(recordFromQuery(query));
        }
    } else {
        logError("查询识别记录", query.lastError());
    }
    
    return records;
}

QList<DetectionRecord> DatabaseManager::getDetectionRecordsBySession(const QString& sessionId) const
{
    QVariantMap conditions;
    conditions["session_id"] = sessionId;
    return queryDetectionRecords(conditions);
}

QList<DetectionRecord> DatabaseManager::getDetectionRecordsByTimeRange(const QDateTime& startTime, 
                                                                       const QDateTime& endTime) const
{
    QMutexLocker locker(&mutex_);
    
    QList<DetectionRecord> records;
    
    if (!connected_) {
        return records;
    }
    
    QSqlQuery query = prepareQuery(
        "SELECT * FROM detection_records WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp DESC"
    );
    query.addBindValue(startTime);
    query.addBindValue(endTime);
    
    if (query.exec()) {
        while (query.next()) {
            records.append(recordFromQuery(query));
        }
    } else {
        logError("按时间范围查询识别记录", query.lastError());
    }
    
    return records;
}

bool DatabaseManager::updateDetectionRecord(const DetectionRecord& record)
{
    QMutexLocker locker(&mutex_);
    
    if (!connected_ || record.id <= 0) {
        return false;
    }
    
    QSqlQuery query = prepareQuery(
        "UPDATE detection_records SET "
        "session_id = ?, timestamp = ?, image_hash = ?, image_path = ?, "
        "model_name = ?, model_type = ?, engine_type = ?, detection_results = ?, "
        "processing_time = ?, confidence = ?, detection_count = ?, notes = ? "
        "WHERE id = ?"
    );
    
    query.addBindValue(record.sessionId);
    query.addBindValue(record.timestamp);
    query.addBindValue(record.imageHash);
    query.addBindValue(record.imagePath);
    query.addBindValue(record.modelName);
    query.addBindValue(record.modelType);
    query.addBindValue(record.engineType);
    query.addBindValue(QJsonDocument(record.detectionResults).toJson(QJsonDocument::Compact));
    query.addBindValue(record.processingTime);
    query.addBindValue(record.confidence);
    query.addBindValue(record.detectionCount);
    query.addBindValue(record.notes);
    query.addBindValue(record.id);
    
    if (query.exec()) {
        emit recordUpdated("detection_records", record.id);
        qDebug() << "更新识别记录成功，ID:" << record.id;
        return true;
    } else {
        logError("更新识别记录", query.lastError());
        return false;
    }
}

bool DatabaseManager::deleteDetectionRecord(int id)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery("DELETE FROM detection_records WHERE id = ?");
    query.addBindValue(id);

    if (query.exec()) {
        emit recordDeleted("detection_records", id);
        qDebug() << "删除识别记录成功，ID:" << id;
        return true;
    } else {
        logError("删除识别记录", query.lastError());
        return false;
    }
}

bool DatabaseManager::clearDetectionRecords()
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery("DELETE FROM detection_records");

    if (query.exec()) {
        qDebug() << "清空识别记录成功";
        return true;
    } else {
        logError("清空识别记录", query.lastError());
        return false;
    }
}

QVariantMap DatabaseManager::getDetectionStatistics() const
{
    QMutexLocker locker(&mutex_);

    QVariantMap stats;

    if (!connected_) {
        return stats;
    }

    // 总记录数
    QSqlQuery query = prepareQuery("SELECT COUNT(*) FROM detection_records");
    if (query.exec() && query.next()) {
        stats["total_records"] = query.value(0).toInt();
    }

    // 今日记录数
    query = prepareQuery(
        "SELECT COUNT(*) FROM detection_records WHERE DATE(timestamp) = DATE('now')"
    );
    if (query.exec() && query.next()) {
        stats["today_records"] = query.value(0).toInt();
    }

    // 平均处理时间
    query = prepareQuery("SELECT AVG(processing_time) FROM detection_records");
    if (query.exec() && query.next()) {
        stats["average_processing_time"] = query.value(0).toFloat();
    }

    // 平均置信度
    query = prepareQuery("SELECT AVG(confidence) FROM detection_records");
    if (query.exec() && query.next()) {
        stats["average_confidence"] = query.value(0).toFloat();
    }

    // 最常用的模型
    query = prepareQuery(
        "SELECT model_name, COUNT(*) as count FROM detection_records "
        "GROUP BY model_name ORDER BY count DESC LIMIT 1"
    );
    if (query.exec() && query.next()) {
        stats["most_used_model"] = query.value(0).toString();
        stats["most_used_model_count"] = query.value(1).toInt();
    }

    return stats;
}

// ========== 模型配置管理 ==========

int DatabaseManager::addModelConfig(const ModelConfig& config)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return -1;
    }

    QSqlQuery query = prepareQuery(
        "INSERT INTO model_configs "
        "(model_name, model_path, labels_path, model_type, engine_type, "
        "parameters, is_default, is_enabled, created_at, updated_at) "
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    );

    QDateTime now = QDateTime::currentDateTime();

    query.addBindValue(config.modelName);
    query.addBindValue(config.modelPath);
    query.addBindValue(config.labelsPath);
    query.addBindValue(config.modelType);
    query.addBindValue(config.engineType);
    query.addBindValue(QJsonDocument(config.parameters).toJson(QJsonDocument::Compact));
    query.addBindValue(config.isDefault);
    query.addBindValue(config.isEnabled);
    query.addBindValue(now);
    query.addBindValue(now);

    if (!query.exec()) {
        logError("添加模型配置", query.lastError());
        return -1;
    }

    int configId = query.lastInsertId().toInt();
    emit recordAdded("model_configs", configId);

    qDebug() << "添加模型配置成功，ID:" << configId;
    return configId;
}

ModelConfig DatabaseManager::getModelConfig(int id) const
{
    QMutexLocker locker(&mutex_);

    ModelConfig config;

    if (!connected_) {
        return config;
    }

    QSqlQuery query = prepareQuery("SELECT * FROM model_configs WHERE id = ?");
    query.addBindValue(id);

    if (query.exec() && query.next()) {
        config = modelConfigFromQuery(query);
    } else {
        logError("获取模型配置", query.lastError());
    }

    return config;
}

ModelConfig DatabaseManager::getModelConfigByName(const QString& modelName) const
{
    QMutexLocker locker(&mutex_);

    ModelConfig config;

    if (!connected_) {
        return config;
    }

    QSqlQuery query = prepareQuery("SELECT * FROM model_configs WHERE model_name = ?");
    query.addBindValue(modelName);

    if (query.exec() && query.next()) {
        config = modelConfigFromQuery(query);
    } else {
        logError("根据名称获取模型配置", query.lastError());
    }

    return config;
}

QList<ModelConfig> DatabaseManager::getAllModelConfigs() const
{
    QMutexLocker locker(&mutex_);

    QList<ModelConfig> configs;

    if (!connected_) {
        return configs;
    }

    QSqlQuery query = prepareQuery("SELECT * FROM model_configs ORDER BY model_name");

    if (query.exec()) {
        while (query.next()) {
            configs.append(modelConfigFromQuery(query));
        }
    } else {
        logError("获取所有模型配置", query.lastError());
    }

    return configs;
}

QList<ModelConfig> DatabaseManager::getModelConfigsByType(const QString& modelType) const
{
    QMutexLocker locker(&mutex_);

    QList<ModelConfig> configs;

    if (!connected_) {
        return configs;
    }

    QSqlQuery query = prepareQuery(
        "SELECT * FROM model_configs WHERE model_type = ? ORDER BY model_name"
    );
    query.addBindValue(modelType);

    if (query.exec()) {
        while (query.next()) {
            configs.append(modelConfigFromQuery(query));
        }
    } else {
        logError("根据类型获取模型配置", query.lastError());
    }

    return configs;
}

ModelConfig DatabaseManager::getDefaultModelConfig(const QString& modelType) const
{
    QMutexLocker locker(&mutex_);

    ModelConfig config;

    if (!connected_) {
        return config;
    }

    QSqlQuery query = prepareQuery(
        "SELECT * FROM model_configs WHERE model_type = ? AND is_default = 1 LIMIT 1"
    );
    query.addBindValue(modelType);

    if (query.exec() && query.next()) {
        config = modelConfigFromQuery(query);
    } else {
        logError("获取默认模型配置", query.lastError());
    }

    return config;
}

bool DatabaseManager::updateModelConfig(const ModelConfig& config)
{
    QMutexLocker locker(&mutex_);

    if (!connected_ || config.id <= 0) {
        return false;
    }

    QSqlQuery query = prepareQuery(
        "UPDATE model_configs SET "
        "model_name = ?, model_path = ?, labels_path = ?, model_type = ?, "
        "engine_type = ?, parameters = ?, is_default = ?, is_enabled = ?, updated_at = ? "
        "WHERE id = ?"
    );

    query.addBindValue(config.modelName);
    query.addBindValue(config.modelPath);
    query.addBindValue(config.labelsPath);
    query.addBindValue(config.modelType);
    query.addBindValue(config.engineType);
    query.addBindValue(QJsonDocument(config.parameters).toJson(QJsonDocument::Compact));
    query.addBindValue(config.isDefault);
    query.addBindValue(config.isEnabled);
    query.addBindValue(QDateTime::currentDateTime());
    query.addBindValue(config.id);

    if (query.exec()) {
        emit recordUpdated("model_configs", config.id);
        qDebug() << "更新模型配置成功，ID:" << config.id;
        return true;
    } else {
        logError("更新模型配置", query.lastError());
        return false;
    }
}

bool DatabaseManager::deleteModelConfig(int id)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery("DELETE FROM model_configs WHERE id = ?");
    query.addBindValue(id);

    if (query.exec()) {
        emit recordDeleted("model_configs", id);
        qDebug() << "删除模型配置成功，ID:" << id;
        return true;
    } else {
        logError("删除模型配置", query.lastError());
        return false;
    }
}

bool DatabaseManager::setDefaultModel(const QString& modelName, const QString& modelType)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    // 开始事务
    database_.transaction();

    try {
        // 清除同类型的其他默认模型
        QSqlQuery query1 = prepareQuery(
            "UPDATE model_configs SET is_default = 0 WHERE model_type = ?"
        );
        query1.addBindValue(modelType);

        if (!query1.exec()) {
            database_.rollback();
            logError("清除默认模型标记", query1.lastError());
            return false;
        }

        // 设置新的默认模型
        QSqlQuery query2 = prepareQuery(
            "UPDATE model_configs SET is_default = 1 WHERE model_name = ? AND model_type = ?"
        );
        query2.addBindValue(modelName);
        query2.addBindValue(modelType);

        if (!query2.exec()) {
            database_.rollback();
            logError("设置默认模型", query2.lastError());
            return false;
        }

        // 提交事务
        database_.commit();
        qDebug() << "设置默认模型成功:" << modelName << "类型:" << modelType;
        return true;

    } catch (...) {
        database_.rollback();
        return false;
    }
}

// ========== 用户设置管理 ==========

bool DatabaseManager::setSetting(const QString& category, const QString& key,
                                 const QString& value, const QString& description)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    // 检查设置是否已存在
    QSqlQuery checkQuery = prepareQuery(
        "SELECT id FROM user_settings WHERE category = ? AND key = ?"
    );
    checkQuery.addBindValue(category);
    checkQuery.addBindValue(key);

    QDateTime now = QDateTime::currentDateTime();

    if (checkQuery.exec() && checkQuery.next()) {
        // 更新现有设置
        int id = checkQuery.value(0).toInt();
        QSqlQuery updateQuery = prepareQuery(
            "UPDATE user_settings SET value = ?, description = ?, updated_at = ? WHERE id = ?"
        );
        updateQuery.addBindValue(value);
        updateQuery.addBindValue(description);
        updateQuery.addBindValue(now);
        updateQuery.addBindValue(id);

        if (updateQuery.exec()) {
            emit recordUpdated("user_settings", id);
            return true;
        } else {
            logError("更新用户设置", updateQuery.lastError());
            return false;
        }
    } else {
        // 插入新设置
        QSqlQuery insertQuery = prepareQuery(
            "INSERT INTO user_settings (category, key, value, description, updated_at) "
            "VALUES (?, ?, ?, ?, ?)"
        );
        insertQuery.addBindValue(category);
        insertQuery.addBindValue(key);
        insertQuery.addBindValue(value);
        insertQuery.addBindValue(description);
        insertQuery.addBindValue(now);

        if (insertQuery.exec()) {
            int id = insertQuery.lastInsertId().toInt();
            emit recordAdded("user_settings", id);
            return true;
        } else {
            logError("添加用户设置", insertQuery.lastError());
            return false;
        }
    }
}

QString DatabaseManager::getSetting(const QString& category, const QString& key,
                                   const QString& defaultValue) const
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return defaultValue;
    }

    QSqlQuery query = prepareQuery(
        "SELECT value FROM user_settings WHERE category = ? AND key = ?"
    );
    query.addBindValue(category);
    query.addBindValue(key);

    if (query.exec() && query.next()) {
        return query.value(0).toString();
    }

    return defaultValue;
}

QVariantMap DatabaseManager::getSettings(const QString& category) const
{
    QMutexLocker locker(&mutex_);

    QVariantMap settings;

    if (!connected_) {
        return settings;
    }

    QSqlQuery query = prepareQuery(
        "SELECT key, value FROM user_settings WHERE category = ?"
    );
    query.addBindValue(category);

    if (query.exec()) {
        while (query.next()) {
            QString key = query.value(0).toString();
            QString value = query.value(1).toString();
            settings[key] = value;
        }
    } else {
        logError("获取分类设置", query.lastError());
    }

    return settings;
}

QList<UserSettings> DatabaseManager::getAllSettings() const
{
    QMutexLocker locker(&mutex_);

    QList<UserSettings> settings;

    if (!connected_) {
        return settings;
    }

    QSqlQuery query = prepareQuery(
        "SELECT * FROM user_settings ORDER BY category, key"
    );

    if (query.exec()) {
        while (query.next()) {
            settings.append(userSettingsFromQuery(query));
        }
    } else {
        logError("获取所有用户设置", query.lastError());
    }

    return settings;
}

bool DatabaseManager::deleteSetting(const QString& category, const QString& key)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery(
        "DELETE FROM user_settings WHERE category = ? AND key = ?"
    );
    query.addBindValue(category);
    query.addBindValue(key);

    if (query.exec()) {
        qDebug() << "删除用户设置成功:" << category << "/" << key;
        return true;
    } else {
        logError("删除用户设置", query.lastError());
        return false;
    }
}

bool DatabaseManager::clearSettings(const QString& category)
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery("DELETE FROM user_settings WHERE category = ?");
    query.addBindValue(category);

    if (query.exec()) {
        qDebug() << "清空分类设置成功:" << category;
        return true;
    } else {
        logError("清空分类设置", query.lastError());
        return false;
    }
}

// ========== 数据库维护 ==========

bool DatabaseManager::backupDatabase(const QString& backupPath) const
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    // 确保备份目录存在
    QDir().mkpath(QFileInfo(backupPath).absolutePath());

    // 复制数据库文件
    if (QFile::copy(databasePath_, backupPath)) {
        qDebug() << "数据库备份成功:" << backupPath;
        return true;
    } else {
        qDebug() << "数据库备份失败:" << backupPath;
        return false;
    }
}

bool DatabaseManager::restoreDatabase(const QString& backupPath)
{
    if (!QFileInfo::exists(backupPath)) {
        qDebug() << "备份文件不存在:" << backupPath;
        return false;
    }

    // 关闭当前连接
    close();

    // 删除当前数据库文件
    if (QFileInfo::exists(databasePath_)) {
        QFile::remove(databasePath_);
    }

    // 复制备份文件
    if (QFile::copy(backupPath, databasePath_)) {
        qDebug() << "数据库恢复成功，从:" << backupPath;
        // 重新初始化
        return initialize(databasePath_);
    } else {
        qDebug() << "数据库恢复失败";
        return false;
    }
}

bool DatabaseManager::optimizeDatabase()
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery("VACUUM");

    if (query.exec()) {
        qDebug() << "数据库优化成功";
        return true;
    } else {
        logError("数据库优化", query.lastError());
        return false;
    }
}

QVariantMap DatabaseManager::getDatabaseInfo() const
{
    QMutexLocker locker(&mutex_);

    QVariantMap info;

    if (!connected_) {
        return info;
    }

    info["database_path"] = databasePath_;
    info["connected"] = connected_;

    // 数据库大小
    QFileInfo fileInfo(databasePath_);
    if (fileInfo.exists()) {
        info["file_size"] = fileInfo.size();
        info["last_modified"] = fileInfo.lastModified();
    }

    // 表信息
    QSqlQuery query = prepareQuery(
        "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
    );

    QStringList tables;
    if (query.exec()) {
        while (query.next()) {
            tables.append(query.value(0).toString());
        }
    }
    info["tables"] = tables;

    // 记录统计
    for (const QString& table : tables) {
        QSqlQuery countQuery = prepareQuery(QString("SELECT COUNT(*) FROM %1").arg(table));
        if (countQuery.exec() && countQuery.next()) {
            info[QString("%1_count").arg(table)] = countQuery.value(0).toInt();
        }
    }

    return info;
}

bool DatabaseManager::checkDatabaseIntegrity() const
{
    QMutexLocker locker(&mutex_);

    if (!connected_) {
        return false;
    }

    QSqlQuery query = prepareQuery("PRAGMA integrity_check");

    if (query.exec() && query.next()) {
        QString result = query.value(0).toString();
        bool isOk = (result == "ok");

        if (isOk) {
            qDebug() << "数据库完整性检查通过";
        } else {
            qDebug() << "数据库完整性检查失败:" << result;
        }

        return isOk;
    } else {
        logError("数据库完整性检查", query.lastError());
        return false;
    }
}

// ========== 私有方法实现 ==========

bool DatabaseManager::createTables()
{
    return createDetectionRecordsTable() &&
           createModelConfigsTable() &&
           createUserSettingsTable();
}

bool DatabaseManager::createDetectionRecordsTable()
{
    QSqlQuery query(database_);

    QString sql = R"(
        CREATE TABLE IF NOT EXISTS detection_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            timestamp DATETIME NOT NULL,
            image_hash TEXT,
            image_path TEXT,
            model_name TEXT NOT NULL,
            model_type TEXT NOT NULL,
            engine_type TEXT NOT NULL,
            detection_results TEXT,
            processing_time REAL DEFAULT 0.0,
            confidence REAL DEFAULT 0.0,
            detection_count INTEGER DEFAULT 0,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    )";

    if (!query.exec(sql)) {
        logError("创建识别记录表", query.lastError());
        return false;
    }

    qDebug() << "识别记录表创建成功";
    return true;
}

bool DatabaseManager::createModelConfigsTable()
{
    QSqlQuery query(database_);

    QString sql = R"(
        CREATE TABLE IF NOT EXISTS model_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            model_name TEXT UNIQUE NOT NULL,
            model_path TEXT NOT NULL,
            labels_path TEXT,
            model_type TEXT NOT NULL,
            engine_type TEXT NOT NULL,
            parameters TEXT,
            is_default BOOLEAN DEFAULT 0,
            is_enabled BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    )";

    if (!query.exec(sql)) {
        logError("创建模型配置表", query.lastError());
        return false;
    }

    qDebug() << "模型配置表创建成功";
    return true;
}

bool DatabaseManager::createUserSettingsTable()
{
    QSqlQuery query(database_);

    QString sql = R"(
        CREATE TABLE IF NOT EXISTS user_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
        )
    )";

    if (!query.exec(sql)) {
        logError("创建用户设置表", query.lastError());
        return false;
    }

    qDebug() << "用户设置表创建成功";
    return true;
}

bool DatabaseManager::createIndexes()
{
    QStringList indexes = {
        "CREATE INDEX IF NOT EXISTS idx_detection_records_timestamp ON detection_records(timestamp)",
        "CREATE INDEX IF NOT EXISTS idx_detection_records_session_id ON detection_records(session_id)",
        "CREATE INDEX IF NOT EXISTS idx_detection_records_model_name ON detection_records(model_name)",
        "CREATE INDEX IF NOT EXISTS idx_model_configs_type ON model_configs(model_type)",
        "CREATE INDEX IF NOT EXISTS idx_model_configs_engine ON model_configs(engine_type)",
        "CREATE INDEX IF NOT EXISTS idx_user_settings_category ON user_settings(category)"
    };

    QSqlQuery query(database_);

    for (const QString& indexSql : indexes) {
        if (!query.exec(indexSql)) {
            qDebug() << "创建索引失败:" << indexSql << query.lastError().text();
            return false;
        }
    }

    qDebug() << "数据库索引创建成功";
    return true;
}

bool DatabaseManager::executeQuery(const QString& queryString, const QVariantList& bindValues) const
{
    QSqlQuery query = prepareQuery(queryString);

    for (const QVariant& value : bindValues) {
        query.addBindValue(value);
    }

    return query.exec();
}

QSqlQuery DatabaseManager::prepareQuery(const QString& queryString) const
{
    QSqlQuery query(database_);
    query.prepare(queryString);
    return query;
}

DetectionRecord DatabaseManager::recordFromQuery(const QSqlQuery& query) const
{
    DetectionRecord record;

    record.id = query.value("id").toInt();
    record.sessionId = query.value("session_id").toString();
    record.timestamp = query.value("timestamp").toDateTime();
    record.imageHash = query.value("image_hash").toString();
    record.imagePath = query.value("image_path").toString();
    record.modelName = query.value("model_name").toString();
    record.modelType = query.value("model_type").toString();
    record.engineType = query.value("engine_type").toString();

    // 解析JSON结果
    QString resultsJson = query.value("detection_results").toString();
    if (!resultsJson.isEmpty()) {
        QJsonDocument doc = QJsonDocument::fromJson(resultsJson.toUtf8());
        record.detectionResults = doc.object();
    }

    record.processingTime = query.value("processing_time").toFloat();
    record.confidence = query.value("confidence").toFloat();
    record.detectionCount = query.value("detection_count").toInt();
    record.notes = query.value("notes").toString();

    return record;
}

ModelConfig DatabaseManager::modelConfigFromQuery(const QSqlQuery& query) const
{
    ModelConfig config;

    config.id = query.value("id").toInt();
    config.modelName = query.value("model_name").toString();
    config.modelPath = query.value("model_path").toString();
    config.labelsPath = query.value("labels_path").toString();
    config.modelType = query.value("model_type").toString();
    config.engineType = query.value("engine_type").toString();

    // 解析JSON参数
    QString parametersJson = query.value("parameters").toString();
    if (!parametersJson.isEmpty()) {
        QJsonDocument doc = QJsonDocument::fromJson(parametersJson.toUtf8());
        config.parameters = doc.object();
    }

    config.isDefault = query.value("is_default").toBool();
    config.isEnabled = query.value("is_enabled").toBool();
    config.createdAt = query.value("created_at").toDateTime();
    config.updatedAt = query.value("updated_at").toDateTime();

    return config;
}

UserSettings DatabaseManager::userSettingsFromQuery(const QSqlQuery& query) const
{
    UserSettings settings;

    settings.id = query.value("id").toInt();
    settings.category = query.value("category").toString();
    settings.key = query.value("key").toString();
    settings.value = query.value("value").toString();
    settings.description = query.value("description").toString();
    settings.updatedAt = query.value("updated_at").toDateTime();

    return settings;
}

void DatabaseManager::logError(const QString& operation, const QSqlError& error) const
{
    QString errorMsg = QString("数据库操作失败 [%1]: %2").arg(operation, error.text());
    qDebug() << errorMsg;

    // 发出错误信号
    const_cast<DatabaseManager*>(this)->emit databaseError(errorMsg);
}

QString DatabaseManager::generateConnectionName() const
{
    return QString("DatabaseConnection_%1").arg(QUuid::createUuid().toString(QUuid::WithoutBraces));
}
