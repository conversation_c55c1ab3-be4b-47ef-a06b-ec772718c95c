#include "TensorFlowLiteModelManager.h"
#include <QDebug>
#include <QStandardPaths>
#include <QJsonArray>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QFile>
#include <QTextStream>
#include <QApplication>

#if defined(_MSC_VER)
#pragma execution_character_set("utf-8")
#endif

TensorFlowLiteModelManager::TensorFlowLiteModelManager(QObject* parent)
    : QObject(parent)
    , settings_(new QSettings("AIVisionPlatform", "TensorFlowLiteModels", this))
    , networkManager_(new QNetworkAccessManager(this))
    , currentDownload_(nullptr)
{
    initializeDirectories();
    loadConfiguration();
    qDebug() << "TensorFlow Lite Model Manager initialized";
}

TensorFlowLiteModelManager::~TensorFlowLiteModelManager()
{
    saveConfiguration();
    if (currentDownload_) {
        currentDownload_->abort();
    }
}

void TensorFlowLiteModelManager::initializeDirectories()
{
    // 设置模型目录
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    modelsDirectory_ = QDir(appDataPath).filePath("models/tensorflow_lite");
    
    // 确保目录存在
    QDir().mkpath(modelsDirectory_);
    
    // 设置配置文件路径
    configFilePath_ = QDir(modelsDirectory_).filePath("models_config.json");
    
    qDebug() << "TensorFlow Lite模型目录:" << modelsDirectory_;
    qDebug() << "配置文件路径:" << configFilePath_;
}

void TensorFlowLiteModelManager::scanModels()
{
    availableModels_.clear();
    
    // 扫描项目models目录
    scanModelsDirectory();
    
    // 加载预定义模型
    loadPredefinedModels();
    
    // 扫描用户模型目录
    QDir userModelsDir(modelsDirectory_);
    if (userModelsDir.exists()) {
        QStringList filters;
        filters << "*.tflite" << "*.lite";
        
        QFileInfoList files = userModelsDir.entryInfoList(filters, QDir::Files);
        for (const QFileInfo& file : files) {
            TFLiteModelInfo modelInfo;
            modelInfo.name = file.baseName();
            modelInfo.path = file.absoluteFilePath();
            modelInfo.type = detectModelTypeFromFile(file.absoluteFilePath());
            modelInfo.description = QString("用户模型: %1").arg(modelInfo.name);
            
            // 查找对应的标签文件
            QString labelsPath = QDir(file.absolutePath()).filePath(file.baseName() + "_labels.txt");
            if (QFileInfo::exists(labelsPath)) {
                modelInfo.labelsPath = labelsPath;
            }
            
            // 查找配置文件
            QString configPath = generateModelConfigPath(modelInfo.name);
            if (QFileInfo::exists(configPath)) {
                TFLiteModelInfo configInfo = parseModelConfig(configPath);
                if (!configInfo.name.isEmpty()) {
                    modelInfo = configInfo;
                    modelInfo.path = file.absoluteFilePath(); // 确保路径正确
                }
            }
            
            availableModels_.append(modelInfo);
        }
    }
    
    qDebug() << "扫描完成，找到" << availableModels_.size() << "个TensorFlow Lite模型";
    emit modelsScanCompleted(availableModels_.size());
}

void TensorFlowLiteModelManager::scanModelsDirectory()
{
    // 扫描项目根目录下的models文件夹
    QString projectModelsDir = QDir::currentPath() + "/models";
    QDir dir(projectModelsDir);
    
    if (dir.exists()) {
        QStringList filters;
        filters << "*.tflite" << "*.lite";
        
        QFileInfoList files = dir.entryInfoList(filters, QDir::Files);
        for (const QFileInfo& file : files) {
            TFLiteModelInfo modelInfo;
            modelInfo.name = file.baseName();
            modelInfo.path = file.absoluteFilePath();
            modelInfo.type = detectModelTypeFromFile(file.absoluteFilePath());
            modelInfo.description = QString("项目模型: %1").arg(modelInfo.name);
            
            // 查找标签文件
            QString labelsPath = QDir(file.absolutePath()).filePath(file.baseName() + "_labels.txt");
            if (!QFileInfo::exists(labelsPath)) {
                // 尝试通用标签文件
                labelsPath = QDir(file.absolutePath()).filePath("coco_labels.txt");
                if (QFileInfo::exists(labelsPath)) {
                    modelInfo.labelsPath = labelsPath;
                }
            } else {
                modelInfo.labelsPath = labelsPath;
            }
            
            availableModels_.append(modelInfo);
        }
    }
}

void TensorFlowLiteModelManager::loadPredefinedModels()
{
    QList<TFLiteModelInfo> predefined = getPredefinedModels();
    for (const TFLiteModelInfo& model : predefined) {
        // 检查模型文件是否存在
        if (QFileInfo::exists(model.path)) {
            availableModels_.append(model);
        }
    }
}

QList<TFLiteModelInfo> TensorFlowLiteModelManager::getModelsByType(TensorFlowLiteEngine::ModelType type) const
{
    QList<TFLiteModelInfo> result;
    for (const TFLiteModelInfo& model : availableModels_) {
        if (model.type == type) {
            result.append(model);
        }
    }
    return result;
}

TFLiteModelInfo TensorFlowLiteModelManager::getDefaultModel(TensorFlowLiteEngine::ModelType type) const
{
    // 首先查找标记为默认的模型
    for (const TFLiteModelInfo& model : availableModels_) {
        if (model.type == type && model.isDefault) {
            return model;
        }
    }
    
    // 如果没有默认模型，返回第一个匹配类型的模型
    QList<TFLiteModelInfo> models = getModelsByType(type);
    if (!models.isEmpty()) {
        return models.first();
    }
    
    return TFLiteModelInfo();
}

TFLiteModelInfo TensorFlowLiteModelManager::getModelByName(const QString& name) const
{
    for (const TFLiteModelInfo& model : availableModels_) {
        if (model.name == name) {
            return model;
        }
    }
    return TFLiteModelInfo();
}

void TensorFlowLiteModelManager::setDefaultModel(const QString& modelName, TensorFlowLiteEngine::ModelType type)
{
    // 清除同类型的其他默认模型
    for (TFLiteModelInfo& model : availableModels_) {
        if (model.type == type) {
            model.isDefault = (model.name == modelName);
        }
    }
    
    saveConfiguration();
    qDebug() << "设置默认模型:" << modelName << "类型:" << modelTypeToString(type);
}

bool TensorFlowLiteModelManager::addCustomModel(const TFLiteModelInfo& modelInfo)
{
    // 验证模型文件
    if (!validateModel(modelInfo.path)) {
        emit errorOccurred(QString("无效的模型文件: %1").arg(modelInfo.path));
        return false;
    }
    
    // 检查是否已存在同名模型
    for (const TFLiteModelInfo& existing : availableModels_) {
        if (existing.name == modelInfo.name) {
            emit errorOccurred(QString("模型名称已存在: %1").arg(modelInfo.name));
            return false;
        }
    }
    
    // 复制模型文件到用户目录
    QString targetPath = QDir(modelsDirectory_).filePath(QFileInfo(modelInfo.path).fileName());
    if (!copyModelFiles(modelInfo.path, targetPath)) {
        emit errorOccurred(QString("复制模型文件失败: %1").arg(modelInfo.path));
        return false;
    }
    
    // 创建新的模型信息
    TFLiteModelInfo newModel = modelInfo;
    newModel.path = targetPath;
    
    // 保存配置文件
    saveModelConfig(newModel);
    
    // 添加到列表
    availableModels_.append(newModel);
    
    qDebug() << "添加自定义模型成功:" << newModel.name;
    return true;
}

bool TensorFlowLiteModelManager::removeModel(const QString& modelName)
{
    for (int i = 0; i < availableModels_.size(); ++i) {
        if (availableModels_[i].name == modelName) {
            TFLiteModelInfo model = availableModels_[i];
            
            // 删除模型文件（仅删除用户目录中的文件）
            if (model.path.startsWith(modelsDirectory_)) {
                QFile::remove(model.path);
                if (!model.labelsPath.isEmpty()) {
                    QFile::remove(model.labelsPath);
                }
                
                // 删除配置文件
                QString configPath = generateModelConfigPath(modelName);
                QFile::remove(configPath);
            }
            
            // 从列表中移除
            availableModels_.removeAt(i);
            
            qDebug() << "移除模型成功:" << modelName;
            return true;
        }
    }
    
    emit errorOccurred(QString("未找到模型: %1").arg(modelName));
    return false;
}

bool TensorFlowLiteModelManager::validateModel(const QString& modelPath) const
{
    QFileInfo fileInfo(modelPath);
    
    // 检查文件是否存在
    if (!fileInfo.exists()) {
        return false;
    }
    
    // 检查文件扩展名
    QString suffix = fileInfo.suffix().toLower();
    if (suffix != "tflite" && suffix != "lite") {
        return false;
    }
    
    // 检查文件大小（至少应该有一些内容）
    if (fileInfo.size() < 1024) { // 小于1KB可能不是有效模型
        return false;
    }
    
    return true;
}

QString TensorFlowLiteModelManager::getModelConfigPath() const
{
    return configFilePath_;
}

void TensorFlowLiteModelManager::saveConfiguration()
{
    QJsonObject config;
    QJsonArray modelsArray;

    for (const TFLiteModelInfo& model : availableModels_) {
        QJsonObject modelObj;
        modelObj["name"] = model.name;
        modelObj["path"] = model.path;
        modelObj["labelsPath"] = model.labelsPath;
        modelObj["type"] = modelTypeToString(model.type);
        modelObj["description"] = model.description;
        modelObj["version"] = model.version;
        modelObj["author"] = model.author;
        modelObj["license"] = model.license;
        modelObj["isDefault"] = model.isDefault;

        QJsonArray inputShapeArray;
        for (const QString& dim : model.inputShape) {
            inputShapeArray.append(dim);
        }
        modelObj["inputShape"] = inputShapeArray;

        QJsonArray outputShapeArray;
        for (const QString& dim : model.outputShape) {
            outputShapeArray.append(dim);
        }
        modelObj["outputShape"] = outputShapeArray;

        modelsArray.append(modelObj);
    }

    config["models"] = modelsArray;
    config["version"] = "1.0";
    config["lastUpdated"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(config);

    QFile file(configFilePath_);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "模型配置已保存到:" << configFilePath_;
    } else {
        qDebug() << "保存模型配置失败:" << configFilePath_;
    }
}

void TensorFlowLiteModelManager::loadConfiguration()
{
    QFile file(configFilePath_);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "配置文件不存在，将创建新配置:" << configFilePath_;
        return;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (!doc.isObject()) {
        qDebug() << "配置文件格式错误";
        return;
    }

    QJsonObject config = doc.object();
    QJsonArray modelsArray = config["models"].toArray();

    for (const QJsonValue& value : modelsArray) {
        QJsonObject modelObj = value.toObject();

        TFLiteModelInfo model;
        model.name = modelObj["name"].toString();
        model.path = modelObj["path"].toString();
        model.labelsPath = modelObj["labelsPath"].toString();
        model.type = stringToModelType(modelObj["type"].toString());
        model.description = modelObj["description"].toString();
        model.version = modelObj["version"].toString();
        model.author = modelObj["author"].toString();
        model.license = modelObj["license"].toString();
        model.isDefault = modelObj["isDefault"].toBool();

        QJsonArray inputShapeArray = modelObj["inputShape"].toArray();
        for (const QJsonValue& dim : inputShapeArray) {
            model.inputShape.append(dim.toString());
        }

        QJsonArray outputShapeArray = modelObj["outputShape"].toArray();
        for (const QJsonValue& dim : outputShapeArray) {
            model.outputShape.append(dim.toString());
        }

        // 验证模型文件是否仍然存在
        if (QFileInfo::exists(model.path)) {
            availableModels_.append(model);
        }
    }

    qDebug() << "从配置文件加载了" << availableModels_.size() << "个模型";
}

QList<TFLiteModelInfo> TensorFlowLiteModelManager::getPredefinedModels()
{
    QList<TFLiteModelInfo> models;

    // MobileNet V1 图像分类模型
    TFLiteModelInfo mobilenet;
    mobilenet.name = "MobileNet V1";
    mobilenet.path = QDir::currentPath() + "/models/mobilenet_v1_224_quant.tflite";
    mobilenet.labelsPath = QDir::currentPath() + "/models/imagenet_labels.txt";
    mobilenet.type = TensorFlowLiteEngine::ImageClassification;
    mobilenet.description = "MobileNet V1 量化模型，用于ImageNet图像分类";
    mobilenet.inputShape = QStringList{"1", "224", "224", "3"};
    mobilenet.outputShape = QStringList{"1", "1001"};
    mobilenet.version = "1.0";
    mobilenet.author = "Google";
    mobilenet.license = "Apache 2.0";
    models.append(mobilenet);

    // 测试模型
    TFLiteModelInfo testModel;
    testModel.name = "Test Model";
    testModel.path = QDir::currentPath() + "/models/test_model.tflite";
    testModel.type = TensorFlowLiteEngine::ImageClassification;
    testModel.description = "测试用模型";
    testModel.version = "1.0";
    models.append(testModel);

    return models;
}

void TensorFlowLiteModelManager::downloadModel(const QString& modelName, const QString& downloadUrl)
{
    if (currentDownload_) {
        emit errorOccurred("已有下载任务在进行中");
        return;
    }

    currentDownloadModel_ = modelName;

    QNetworkRequest request(downloadUrl);
    request.setRawHeader("User-Agent", "AIVisionPlatform/1.0");

    currentDownload_ = networkManager_->get(request);

    connect(currentDownload_, &QNetworkReply::downloadProgress,
            this, &TensorFlowLiteModelManager::onDownloadProgress);
    connect(currentDownload_, &QNetworkReply::finished,
            this, &TensorFlowLiteModelManager::onDownloadFinished);

    qDebug() << "开始下载模型:" << modelName << "从" << downloadUrl;
}

void TensorFlowLiteModelManager::onDownloadProgress(qint64 bytesReceived, qint64 bytesTotal)
{
    if (bytesTotal > 0) {
        int progress = static_cast<int>((bytesReceived * 100) / bytesTotal);
        emit modelDownloadProgress(currentDownloadModel_, progress);
    }
}

void TensorFlowLiteModelManager::onDownloadFinished()
{
    if (!currentDownload_) {
        return;
    }

    bool success = false;
    QString errorMsg;

    if (currentDownload_->error() == QNetworkReply::NoError) {
        // 保存下载的文件
        QString fileName = currentDownloadModel_ + ".tflite";
        QString filePath = QDir(modelsDirectory_).filePath(fileName);

        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly)) {
            file.write(currentDownload_->readAll());
            file.close();

            // 验证下载的模型
            if (validateModel(filePath)) {
                // 创建模型信息
                TFLiteModelInfo modelInfo;
                modelInfo.name = currentDownloadModel_;
                modelInfo.path = filePath;
                modelInfo.type = detectModelTypeFromName(currentDownloadModel_);
                modelInfo.description = QString("下载的模型: %1").arg(currentDownloadModel_);

                availableModels_.append(modelInfo);
                saveConfiguration();
                success = true;

                qDebug() << "模型下载并验证成功:" << currentDownloadModel_;
            } else {
                QFile::remove(filePath);
                errorMsg = "下载的文件不是有效的TensorFlow Lite模型";
            }
        } else {
            errorMsg = "无法保存下载的文件";
        }
    } else {
        errorMsg = currentDownload_->errorString();
    }

    emit modelDownloadCompleted(currentDownloadModel_, success);
    if (!success && !errorMsg.isEmpty()) {
        emit errorOccurred(QString("下载模型失败: %1").arg(errorMsg));
    }

    currentDownload_->deleteLater();
    currentDownload_ = nullptr;
    currentDownloadModel_.clear();
}

// 私有方法实现
TFLiteModelInfo TensorFlowLiteModelManager::parseModelConfig(const QString& configPath) const
{
    TFLiteModelInfo model;

    QFile file(configPath);
    if (!file.open(QIODevice::ReadOnly)) {
        return model;
    }

    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);

    if (doc.isObject()) {
        QJsonObject obj = doc.object();
        model.name = obj["name"].toString();
        model.description = obj["description"].toString();
        model.version = obj["version"].toString();
        model.author = obj["author"].toString();
        model.license = obj["license"].toString();
        model.type = stringToModelType(obj["type"].toString());
        model.isDefault = obj["isDefault"].toBool();

        QJsonArray inputArray = obj["inputShape"].toArray();
        for (const QJsonValue& val : inputArray) {
            model.inputShape.append(val.toString());
        }

        QJsonArray outputArray = obj["outputShape"].toArray();
        for (const QJsonValue& val : outputArray) {
            model.outputShape.append(val.toString());
        }
    }

    return model;
}

void TensorFlowLiteModelManager::saveModelConfig(const TFLiteModelInfo& modelInfo) const
{
    QString configPath = generateModelConfigPath(modelInfo.name);

    QJsonObject obj;
    obj["name"] = modelInfo.name;
    obj["description"] = modelInfo.description;
    obj["version"] = modelInfo.version;
    obj["author"] = modelInfo.author;
    obj["license"] = modelInfo.license;
    obj["type"] = modelTypeToString(modelInfo.type);
    obj["isDefault"] = modelInfo.isDefault;

    QJsonArray inputArray;
    for (const QString& dim : modelInfo.inputShape) {
        inputArray.append(dim);
    }
    obj["inputShape"] = inputArray;

    QJsonArray outputArray;
    for (const QString& dim : modelInfo.outputShape) {
        outputArray.append(dim);
    }
    obj["outputShape"] = outputArray;

    QJsonDocument doc(obj);

    QFile file(configPath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
    }
}

QString TensorFlowLiteModelManager::generateModelConfigPath(const QString& modelName) const
{
    return QDir(modelsDirectory_).filePath(modelName + "_config.json");
}

bool TensorFlowLiteModelManager::copyModelFiles(const QString& sourcePath, const QString& targetPath)
{
    return QFile::copy(sourcePath, targetPath);
}

TensorFlowLiteEngine::ModelType TensorFlowLiteModelManager::detectModelTypeFromFile(const QString& modelPath) const
{
    return detectModelTypeFromName(QFileInfo(modelPath).baseName());
}

TensorFlowLiteEngine::ModelType TensorFlowLiteModelManager::detectModelTypeFromName(const QString& modelName) const
{
    QString name = modelName.toLower();

    if (name.contains("classification") || name.contains("mobilenet") ||
        name.contains("resnet") || name.contains("efficientnet") ||
        name.contains("imagenet")) {
        return TensorFlowLiteEngine::ImageClassification;
    } else if (name.contains("detection") || name.contains("yolo") ||
               name.contains("ssd") || name.contains("rcnn") ||
               name.contains("coco")) {
        return TensorFlowLiteEngine::ObjectDetection;
    }

    return TensorFlowLiteEngine::ImageClassification; // 默认为图像分类
}

QString TensorFlowLiteModelManager::modelTypeToString(TensorFlowLiteEngine::ModelType type) const
{
    switch (type) {
    case TensorFlowLiteEngine::ImageClassification:
        return "ImageClassification";
    case TensorFlowLiteEngine::ObjectDetection:
        return "ObjectDetection";
    default:
        return "Unknown";
    }
}

TensorFlowLiteEngine::ModelType TensorFlowLiteModelManager::stringToModelType(const QString& typeStr) const
{
    if (typeStr == "ImageClassification") {
        return TensorFlowLiteEngine::ImageClassification;
    } else if (typeStr == "ObjectDetection") {
        return TensorFlowLiteEngine::ObjectDetection;
    }
    return TensorFlowLiteEngine::Unknown;
}

bool TensorFlowLiteModelManager::isValidModelFile(const QString& filePath) const
{
    return validateModel(filePath);
}

bool TensorFlowLiteModelManager::isValidLabelsFile(const QString& filePath) const
{
    QFileInfo fileInfo(filePath);
    return fileInfo.exists() && fileInfo.suffix().toLower() == "txt";
}
