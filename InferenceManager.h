#pragma once

#include <QObject>
#include <QTimer>
#include <QMutex>
#include <QHash>
#include <QList>
#include <memory>

#include "InferenceWorker.h"
#include "TensorFlowLiteEngine.h"
#include "TensorFlowLiteModelManager.h"
#include "OpenCVVisionEngine.h"

/**
 * @brief 推理管理器配置
 */
struct InferenceManagerConfig {
    int maxWorkerThreads = 2;           // 最大工作线程数
    int maxQueueSizePerWorker = 50;     // 每个工作线程的最大队列大小
    bool enableLoadBalancing = true;     // 启用负载均衡
    bool enableAutoScaling = true;       // 启用自动扩缩容
    int statsUpdateInterval = 1000;      // 统计信息更新间隔(ms)
    float cpuThresholdHigh = 80.0f;     // CPU使用率高阈值(%)
    float cpuThresholdLow = 30.0f;      // CPU使用率低阈值(%)
    
    InferenceManagerConfig() = default;
};

/**
 * @brief 推理管理器统计信息
 */
struct InferenceManagerStats {
    int totalWorkers = 0;               // 总工作线程数
    int activeWorkers = 0;              // 活跃工作线程数
    int totalQueueSize = 0;             // 总队列大小
    int totalTasks = 0;                 // 总任务数
    int completedTasks = 0;             // 已完成任务数
    int failedTasks = 0;                // 失败任务数
    float averageProcessingTime = 0.0f; // 平均处理时间
    float systemCpuUsage = 0.0f;        // 系统CPU使用率
    float memoryUsage = 0.0f;           // 内存使用率
    
    InferenceManagerStats() = default;
};

/**
 * @brief 推理管理器
 * 
 * 管理多个推理工作线程，提供负载均衡和自动扩缩容功能
 */
class InferenceManager : public QObject {
    Q_OBJECT

public:
    explicit InferenceManager(QObject* parent = nullptr);
    ~InferenceManager();

    /**
     * @brief 初始化推理管理器
     */
    bool initialize(const InferenceManagerConfig& config = InferenceManagerConfig());

    /**
     * @brief 设置引擎
     */
    void setTensorFlowLiteEngine(TensorFlowLiteEngine* engine);
    void setTensorFlowLiteModelManager(TensorFlowLiteModelManager* manager);
    void setOpenCVEngine(OpenCVVisionEngine* engine);

    /**
     * @brief 提交推理任务
     */
    QString submitTask(InferenceTaskType type, const QImage& image, 
                      int priority = 5, const QVariantMap& parameters = QVariantMap());

    /**
     * @brief 批量提交任务
     */
    QStringList submitBatchTasks(const QList<InferenceTask>& tasks);

    /**
     * @brief 取消任务
     */
    bool cancelTask(const QString& taskId);

    /**
     * @brief 清空所有任务
     */
    void clearAllTasks();

    /**
     * @brief 获取管理器状态
     */
    bool isInitialized() const { return initialized_; }
    bool isRunning() const { return running_; }

    /**
     * @brief 启动/停止管理器
     */
    void start();
    void stop();
    void pause();
    void resume();

    /**
     * @brief 获取统计信息
     */
    InferenceManagerStats getStats() const;
    void resetStats();

    /**
     * @brief 配置管理
     */
    void updateConfig(const InferenceManagerConfig& config);
    InferenceManagerConfig getConfig() const { return config_; }

    /**
     * @brief 工作线程管理
     */
    int getWorkerCount() const;
    void setWorkerCount(int count);
    void addWorker();
    void removeWorker();

    /**
     * @brief 负载均衡
     */
    void enableLoadBalancing(bool enable);
    void rebalanceTasks();

    /**
     * @brief 性能监控
     */
    void enablePerformanceMonitoring(bool enable);
    float getCurrentCpuUsage() const;
    float getCurrentMemoryUsage() const;

signals:
    /**
     * @brief 任务完成信号
     */
    void taskCompleted(const InferenceResult& result);

    /**
     * @brief 任务失败信号
     */
    void taskFailed(const QString& taskId, const QString& errorMessage);

    /**
     * @brief 统计信息更新信号
     */
    void statsUpdated(const InferenceManagerStats& stats);

    /**
     * @brief 管理器状态变化信号
     */
    void managerStateChanged(bool running, int activeWorkers);

    /**
     * @brief 性能警告信号
     */
    void performanceWarning(const QString& message, float value);

    /**
     * @brief 工作线程数量变化信号
     */
    void workerCountChanged(int count);

private slots:
    void onWorkerTaskCompleted(const InferenceResult& result);
    void onWorkerTaskFailed(const QString& taskId, const QString& errorMessage);
    void onWorkerStateChanged(bool running, bool busy);
    void onStatsUpdateTimer();
    void onPerformanceCheckTimer();

private:
    // 配置和状态
    InferenceManagerConfig config_;
    bool initialized_;
    bool running_;
    bool paused_;

    // 引擎
    TensorFlowLiteEngine* tfliteEngine_;
    TensorFlowLiteModelManager* modelManager_;
    OpenCVVisionEngine* opencvEngine_;

    // 工作线程管理
    QList<std::shared_ptr<InferenceWorker>> workers_;
    mutable QMutex workersMutex_;
    QHash<QString, int> taskToWorkerMap_; // 任务ID到工作线程索引的映射

    // 统计信息
    mutable QMutex statsMutex_;
    InferenceManagerStats stats_;

    // 定时器
    QTimer* statsUpdateTimer_;
    QTimer* performanceCheckTimer_;

    // 性能监控
    bool performanceMonitoringEnabled_;
    QList<float> cpuUsageHistory_;
    QList<float> memoryUsageHistory_;

    // 私有方法
    void initializeWorkers();
    void cleanupWorkers();
    int selectBestWorker() const;
    void updateWorkerEngines(std::shared_ptr<InferenceWorker> worker);
    void collectStats();
    void checkPerformance();
    void autoScale();
    bool shouldAddWorker() const;
    bool shouldRemoveWorker() const;
    
    // 负载均衡
    void balanceLoad();
    int getWorkerLoad(int workerIndex) const;
    
    // 系统监控
    float calculateCpuUsage() const;
    float calculateMemoryUsage() const;
    void updatePerformanceHistory(float cpuUsage, float memoryUsage);
};
