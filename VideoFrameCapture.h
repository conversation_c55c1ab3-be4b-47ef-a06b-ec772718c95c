#pragma once

#include <QObject>
#include <QImage>
#include <QVideoSink>
#include <QVideoFrame>
#include <QTimer>
#include <QRectF>
#include <QPainter>

// 简化的检测结构
struct Detection {
    QString className;
    float confidence;
    QRectF boundingBox; // 相对坐标 (0-1)
};

/**
 * @brief 视频帧捕获类
 * 
 * 从Qt6的视频流中捕获帧并转换为QImage
 */
class VideoFrameCapture : public QObject {
    Q_OBJECT

public:
    explicit VideoFrameCapture(QObject* parent = nullptr);
    ~VideoFrameCapture();

    /**
     * @brief 设置视频接收器
     */
    void setVideoSink(QVideoSink* sink);

    /**
     * @brief 获取最新的帧
     */
    QImage getLatestFrame() const;

    /**
     * @brief 是否有可用的帧
     */
    bool hasFrame() const;

    /**
     * @brief 启用/禁用帧捕获
     */
    void setEnabled(bool enabled);
    bool isEnabled() const { return enabled_; }

    /**
     * @brief 设置检测结果
     */
    void setDetections(const std::vector<Detection>& detections);

    /**
     * @brief 在帧上绘制检测结果
     */
    static QImage drawDetectionsOnFrame(const QImage& frame, const std::vector<Detection>& detections);

signals:
    /**
     * @brief 新帧可用
     */
    void frameAvailable(const QImage& frame);

    /**
     * @brief 帧捕获状态改变
     */
    void captureStatusChanged(bool enabled);

private slots:
    /**
     * @brief 处理新的视频帧
     */
    void onVideoFrameChanged(const QVideoFrame& frame);

private:
    QVideoSink* videoSink_;
    QImage latestFrame_;
    bool enabled_;
    bool hasFrame_;
    std::vector<Detection> detections_;
    
    /**
     * @brief 转换视频帧为QImage
     */
    QImage videoFrameToQImage(const QVideoFrame& frame);
};
