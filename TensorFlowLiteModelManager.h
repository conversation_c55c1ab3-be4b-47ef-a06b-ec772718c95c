#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QJsonObject>
#include <QJsonDocument>
#include <QDir>
#include <QFileInfo>
#include <QSettings>
#include "TensorFlowLiteEngine.h"

/**
 * @brief TensorFlow Lite模型信息
 */
struct TFLiteModelInfo {
    QString name;                    // 模型名称
    QString path;                    // 模型路径
    QString labelsPath;              // 标签文件路径
    TensorFlowLiteEngine::ModelType type;  // 模型类型
    QString description;             // 模型描述
    QStringList inputShape;          // 输入形状
    QStringList outputShape;         // 输出形状
    QString version;                 // 模型版本
    QString author;                  // 作者
    QString license;                 // 许可证
    bool isDefault;                  // 是否为默认模型
    
    TFLiteModelInfo() : type(TensorFlowLiteEngine::Unknown), isDefault(false) {}
};

/**
 * @brief TensorFlow Lite模型管理器
 * 
 * 负责管理和配置TensorFlow Lite模型
 */
class TensorFlowLiteModelManager : public QObject {
    Q_OBJECT

public:
    explicit TensorFlowLiteModelManager(QObject* parent = nullptr);
    ~TensorFlowLiteModelManager();

    /**
     * @brief 扫描并加载可用模型
     */
    void scanModels();
    
    /**
     * @brief 获取可用模型列表
     */
    QList<TFLiteModelInfo> getAvailableModels() const { return availableModels_; }
    
    /**
     * @brief 根据类型获取模型列表
     */
    QList<TFLiteModelInfo> getModelsByType(TensorFlowLiteEngine::ModelType type) const;
    
    /**
     * @brief 获取默认模型
     */
    TFLiteModelInfo getDefaultModel(TensorFlowLiteEngine::ModelType type) const;
    
    /**
     * @brief 根据名称获取模型信息
     */
    TFLiteModelInfo getModelByName(const QString& name) const;
    
    /**
     * @brief 设置默认模型
     */
    void setDefaultModel(const QString& modelName, TensorFlowLiteEngine::ModelType type);
    
    /**
     * @brief 添加自定义模型
     */
    bool addCustomModel(const TFLiteModelInfo& modelInfo);
    
    /**
     * @brief 移除模型
     */
    bool removeModel(const QString& modelName);
    
    /**
     * @brief 验证模型文件
     */
    bool validateModel(const QString& modelPath) const;
    
    /**
     * @brief 获取模型配置文件路径
     */
    QString getModelConfigPath() const;
    
    /**
     * @brief 保存模型配置
     */
    void saveConfiguration();
    
    /**
     * @brief 加载模型配置
     */
    void loadConfiguration();
    
    /**
     * @brief 获取预定义模型列表
     */
    static QList<TFLiteModelInfo> getPredefinedModels();
    
    /**
     * @brief 下载预训练模型
     */
    void downloadModel(const QString& modelName, const QString& downloadUrl);

signals:
    /**
     * @brief 模型扫描完成
     */
    void modelsScanCompleted(int modelCount);
    
    /**
     * @brief 模型下载进度
     */
    void modelDownloadProgress(const QString& modelName, int progress);
    
    /**
     * @brief 模型下载完成
     */
    void modelDownloadCompleted(const QString& modelName, bool success);
    
    /**
     * @brief 错误信号
     */
    void errorOccurred(const QString& errorMessage);

private slots:
    void onDownloadProgress(qint64 bytesReceived, qint64 bytesTotal);
    void onDownloadFinished();

private:
    QList<TFLiteModelInfo> availableModels_;
    QSettings* settings_;
    QString modelsDirectory_;
    QString configFilePath_;
    
    // 网络下载相关
    class QNetworkAccessManager* networkManager_;
    class QNetworkReply* currentDownload_;
    QString currentDownloadModel_;
    
    // 私有方法
    void initializeDirectories();
    void scanModelsDirectory();
    void loadPredefinedModels();
    TFLiteModelInfo parseModelConfig(const QString& configPath) const;
    void saveModelConfig(const TFLiteModelInfo& modelInfo) const;
    QString generateModelConfigPath(const QString& modelName) const;
    bool copyModelFiles(const QString& sourcePath, const QString& targetPath);
    
    // 模型类型检测
    TensorFlowLiteEngine::ModelType detectModelTypeFromFile(const QString& modelPath) const;
    TensorFlowLiteEngine::ModelType detectModelTypeFromName(const QString& modelName) const;
    
    // 工具方法
    QString modelTypeToString(TensorFlowLiteEngine::ModelType type) const;
    TensorFlowLiteEngine::ModelType stringToModelType(const QString& typeStr) const;
    bool isValidModelFile(const QString& filePath) const;
    bool isValidLabelsFile(const QString& filePath) const;
};
